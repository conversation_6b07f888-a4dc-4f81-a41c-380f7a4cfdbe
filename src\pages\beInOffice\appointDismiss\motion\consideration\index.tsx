/**
 * 干部任免酝酿环节 - 考虑页面
 */
import { Button, Input, Space, Select, Modal, message, Upload, Checkbox, Popover, Divider, Empty, Form } from 'antd';
import React, { useState, useEffect, useRef, useMemo, Fragment } from 'react';
import styles from './index.less';
import TimePicker from '@/components/TimePicker';
import { Head, Colgroup, fakeLine } from '@/components/DynamicTableHead/index2';
import { isEmpty, debounce, divide } from 'lodash';
import { ArrowUpOutlined, ArrowDownOutlined, DeleteOutlined, PlusOutlined, UploadOutlined, DragOutlined, EditOutlined } from '@ant-design/icons';
import moment from 'moment';
import PersonModalSelect, { EnhancedPersonType } from './components/PersonModalSelect';
import request from '@/utils/request';

import DictSelect from '@/components/DictSelect';
import PositionSelectModal from './components/PositionSelect';
import EditPositionModal from './components/editPositionModal';
import ConfirmInfoModal from './components/PersonModalSelect/ConfirmInfoModal';
import ListTable from '@/components/Hook/ListTable';
import { router } from 'umi';
import { _history } from '@/utils/session';
import _isArray from 'lodash/isArray';
import Gxfa from '@/pages/beInOffice/appointDismiss/index';

import { sharePlan, importPlan, exportPlan, applyThisVersion } from '../../services';
import { fileDownload } from '@/utils/method';
import TextArea from 'antd/lib/input/TextArea';

// 创建空的人员信息对象
const createEmptyPerson = (): PersonInfo => ({ A00: '', A0101: '', XRZW: '' });

// 定义人员信息接口
interface PersonInfo {
  A00?: string;
  A0101?: string;
  XRZW?: string;
  // 扩展数据字段
  extendData1?: any;
  extendData2?: any;
  extendData3?: any;
  extendData?: any;
}

// 定义备选人员接口
interface AlternativeDto {
  code: string;
  name: string;
  extendDto: {
    jobChangeCode?: string;
    jobChange?: string;
    currentPositionCode?: string;
    currentPosition?: string;
    proposePositionCode?: string;
    proposePosition?: string;
    proposedRemovalPositionCode?: string;
    proposedRemovalPosition?: string;
    reason?: string;
    introduction?: string;
    currPerson?: string;
    jobDescription?: string;
  };
}

// 定义第4-6个有关人选接口
interface OtherCandidateItem {
  code: string; // 人员/岗位code
  name: string; // 人员/岗位姓名
  index: number; // 所处人员框位置index的值从4开始
  extendDto: {
    jobChangeCode: string; // 职级变动code
    jobChange: string; // 职级变动
    currentPositionCode: string; // 现任职务code
    currentPosition: string; // 现任职务
    proposePositionCode: string; // 拟任职务code
    proposePosition: string; // 拟任职务
    proposedRemovalPositionCode: string; // 拟免职务code
    proposedRemovalPosition: string; // 拟免职务
    reason: string; // 使用理由
    introduction: string; // 简介
    currPerson: string; // 当前任职人员
    jobDescription: string; // 岗位说明
  };
}

// 定义备选岗位接口
interface JobAlternativeDto {
  code: string;
  name: string;
  extendDto: {
    jobChangeCode: string;
    jobChange: string;
    currentPositionCode: string;
    currentPosition: string;
    proposePositionCode: string;
    proposePosition: string;
    proposedRemovalPositionCode: string;
    proposedRemovalPosition: string;
    reason: string;
    introduction: string;
    currPerson: string;
    jobDescription: string;
  };
}

// 定义行项目接口
interface RowItem {
  id: string;
  code: string;
  type1: string;
  typeCode1: string;
  type2: string;
  typeCode2: string;
  // 岗位相关字段 - 平级结构
  positionCode?: string; // 岗位代码
  positionName?: string; // 岗位名称
  chooseType?: string; // 选择类型：1-以岗找人，2-以人找岗
  jobExtend1?: any; // 岗位扩展信息1
  jobExtend2?: any; // 岗位扩展信息2
  jobExtend3?: any; // 岗位扩展信息3
  personConfirmValues?: any; // 人员确认信息
  persons: PersonInfo[];
  remarksDTO: { rmFile: string; fjFile: string; content: string } | string;
  sort: number;
  candidateNameExtend1?: string;
  candidateNameExtend2?: string;
  candidateNameExtend3?: string;
  extendData?: object;
  extend1?: object;
  extend2?: object;
  extend3?: object;
  CandidateSpecialList?: any; // typeCode1 == '6'||item.typeCode1 == '5'||item.typeCode1 == '4'可以一直添加有关人员
  selectExtend?: any; // 以人找岗模式下人选确认选定岗位的对应扩展数据
  extendDto?: any; // 以人找岗模式下人选确认选定岗位的对应扩展数据
  otherCandidateList?: OtherCandidateItem[]; // 第4-6个有关人选列表
}

// 定义版本记录项接口
interface VersionRecord {
  candidateCode1: string;
  candidateCode2: string;
  candidateCode3: string;
  candidateName1: string;
  candidateName2: string;
  candidateName3: string;
  code: string;
  createTime: string;
  planCode: string;
  positionCode: string;
  positionName: string;
  remarksDTO: string;
  type1: string;
  type2: string;
  typeCode1: string;
  typeCode2: string;
  version: string;
  presentPosition1?: string;
  presentPosition2?: string;
  presentPosition3?: string;
  otherCandidateList?: OtherCandidateItem[]; // 第4-6个有关人选列表
}

// 定义版本历史项接口
interface VersionHistoryItem {
  id: string;
  version: string;
  createTime: string;
  tableTime: string;
  tableUnit: string;
  chooseType?: string;
  versionDesc: string;
  createUser: string;
  records: VersionRecord[];
  key: string;
  index: number;
  CandidateSpecialList?: any[];
  otherCandidateList?: OtherCandidateItem[]; // 第4-6个有关人选列表
}

// 定义表格组件的接口
interface TableComponentProps {
  data: any[];
  isReadOnly?: boolean;
  tableWidth?: string | number;
  onDataChange?: React.Dispatch<React.SetStateAction<any[]>>;
  isResultConfirm?: boolean; // 是否为人选确认模式
}

// 定义组件props接口
interface ConsiderationProps {
  // 控制显示哪些按钮
  buttons?: {
    save?: boolean; // 保存按钮
    preview?: boolean; // 预览按钮
    saveAs?: boolean; // 另存为按钮
    version?: boolean; // 版本管理按钮
    share?: boolean; // 共享按钮
    sharedPlans?: boolean; // 读取共享方案按钮
    importPlan?: boolean; // 导入方案按钮
    exportPlan?: boolean; // 导出方案按钮
    exportIpadData?: boolean; // 导出方案按钮
    resultConfirm?: boolean; // 人选确认按钮
    sort?: boolean; // 排序按钮
    add?: boolean; // 新增按钮
  };
  pageType?: number;
  // 其他props可以在这里添加
}

// 生成唯一ID
const generateUniqueId = () => {
  return `id_${Date.now()}_${Math.random()
    .toString(36)
    .substr(2, 9)}`;
};

const ConsiderationComponent: React.FC<ConsiderationProps> = props => {
  // 设置按钮显示的默认值
  const {
    buttons = {
      save: true,
      preview: true,
      saveAs: true,
      version: true,
      share: true,
      sharedPlans: true,
      importPlan: true,
      exportPlan: true,
      resultConfirm: false,
      exportIpadData: true,
      sort: true,
      isHiddenRightTopCard: false,
    },
    pageType = 1,
  } = props;

  const querys = _history.location.query || {};
  const [importLoading1, setImportLoading1] = useState(false);
  const [fileUrl, setFileUrl] = useState(querys?.fileUrl || ''); // 文件下载路径
  const [htmlContent, setHtmlContent]: any = useState(sessionStorage.getItem('archiveMaterialsFile') || ''); // 从sessionStorage获取html内容

  // 更新数据结构，添加唯一ID
  const [data, setData] = useState<RowItem[]>([]);
  const [currentTime, setCurrentTime] = useState(moment());
  const [positionModalVisible, setPositionModalVisible] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [schemeId, setSchemeId] = useState<string>(); // 修改类型定义
  const [unitName, setUnitName] = useState('市委组织部'); // 制表单位
  const [reportingTime, setReportingTime] = useState(moment()); // 添加汇报时间
  const [reportContent, setReportContent] = useState(''); // 添加汇报内容
  const [alternativeDtoList, setAlternativeDtoList] = useState<AlternativeDto[]>([]); // 方案级别的备选人员列表
  const [alternativePanelVisible, setAlternativePanelVisible] = useState(false); // 控制备选面板显示
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set()); // 控制每行第二行的显示

  // 添加数据加载标志，在加载数据时禁用自动保存
  const [isDataLoading, setIsDataLoading] = useState(false);

  // 添加拖拽类型ref
  const dragTypeRef = useRef<number | undefined>(undefined); // 1: 同行拖拽, 2: 跨行拖拽

  // 添加岗位选择状态
  const [currentRowChooseType, setCurrentRowChooseType] = useState<string>('1'); // 默认为1，表示以人找岗
  const [currentPersonSelectIndex, setCurrentPersonSelectIndex] = useState(0); // 当前选择的人员索引
  const [currentRowData, setCurrentRowData] = useState<RowItem | null>(null); // 当前操作的行数据

  // 添加版本管理状态
  const [versionModalVisible, setVersionModalVisible] = useState(false);
  const [versionList, setVersionList] = useState<VersionHistoryItem[]>([]);
  const [versionLoading, setVersionLoading] = useState(false);

  // 添加人选确认状态
  const [resultConfirmVisible, setResultConfirmVisible] = useState(false);
  const [resultConfirmLoading, setResultConfirmLoading] = useState(false);
  const [resultConfirmData, setResultConfirmData] = useState<any[]>([]);
  // 共享方案
  const [faVisible, setFaVisible] = useState(false); // 共享方案弹窗可见性
  // 操作日志
  const [logVisible, setLogVisible] = useState(false);
  const [logList, setLogList] = useState([]); // 操作日志列表
  const [logLoading, setLogLoading] = useState(false); // 操作日志加载状态
  const [logPagination, setLogPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 备注弹窗相关状态
  const [remarkModalVisible, setRemarkModalVisible] = useState(false);
  const [currentRemarkId, setCurrentRemarkId] = useState<string | null>(null);
  const [remarkForm] = Form.useForm();
  const [rmFileList, setRmFileList] = useState<any[]>([]);
  const [fjFileList, setFjFileList] = useState<any[]>([]);
  // 获取操作日志列表
  const getLogList = async (page = 1, pageSize = 10) => {
    try {
      setLogLoading(true);
      const res = await request(`/api/swzzbappoint/log/findLogs?planCode=${schemeId}&pageNum=${page}&pageSize=${pageSize}`);
      if (res && res.code === 0) {
        setLogList(res?.data?.list || []);
        setLogPagination({
          current: res?.data?.pageNumber || 1,
          pageSize: res?.data?.pageSize || 10,
          total: res.data.totalRow || 0,
        });
      } else {
        message.error('获取操作日志失败');
      }
    } catch (error) {
      console.error('获取操作日志失败:', error);
      message.error('获取操作日志失败，请重试');
    } finally {
      setLogLoading(false);
    }
  };
  const handlePageChangeLog = (page, pageSize) => {
    getLogList(page, pageSize);
  };

  // PersonModalSelect的DOM引用
  const personSelectButtonRef = useRef<HTMLButtonElement>(null);
  // 添加记录当前最大sort值的ref
  const maxSortRef = useRef<number>(-1);

  //控制人员确认信息弹窗
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  //
  const [editPositionModalVisible, setEditPositionModalVisible] = useState(false);
  // 人员的扩展信息 人员信息确认框数据
  const [confirmModalData, setConfirmModalData] = useState<any>({});
  //过滤已经选择的人员
  const [filterPersonList, setFilterPersonList] = useState<string[]>([]);

  // 计算不重复的人员总数
  const calculateTotalPersons = () => {
    // 使用flatMap和过滤器提取所有有效的人员ID
    const allPersonIds = data
      .flatMap(item => {
        const specials = item.typeCode1 == '6' || item.typeCode1 == '5' || item.typeCode1 == '4';
        if (specials) {
          return item.CandidateSpecialList || [];
        }
        return item.persons || [];
      })
      .filter(person => {
        // console.log("🚀 ~ calculateTotalPersonsfilter ~ person:", person)
        return person && (person.A00 || person.candidateCode);
      })
      .map(person => {
        // console.log("🚀 ~ calculateTotalPersonsmap ~ person:", person)
        return person.A00 || person.candidateCode;
      });
    // 使用Set去重
    return new Set(allPersonIds).size;
  };

  // 使用useMemo优化计算
  const totalPersons = useMemo(() => calculateTotalPersons(), [data]);

  useEffect(() => {
    // 设置当前时间
    setCurrentTime(moment());
    // console.log(buttons, 'buttons');
    // 获取URL参数，如果存在code则加载数据

    const { code, type } = _history.location.query;
    //待审核方案 只读、导出功能
    if (type == '4') {
      setAllowClick(true);
    }
    if (code) {
      setSchemeId(code);
      loadScheme(code);
    }
  }, []);

  // 加载方案数据
  const loadScheme = async id => {
    try {
      // 设置数据加载标志，禁用自动保存
      setIsDataLoading(true);
      setLoading(true);

      const res = await request(`/api/swzzbappoint/prepareCadres/findPrepareCadres?planCode=${id}&type=${pageType}`);
      if (res && res.code === 0) {
        // 重置拖拽类型
        dragTypeRef.current = undefined;
        const { tableTime, tableUnit, reportingTime, reportContent, list = [], alternativeDtoList = [], isConfirm } = res.data;

        // 将后端返回的数据转换为本地数据格式
        if (list && list.length > 0) {
          // 创建一个临时数组存储格式化后的数据
          const formattedData: RowItem[] = [];
          // 杨观明 屏蔽  后端反个字段判断
          // const targetProps = ['selectedCandidateCode', 'selectedCandidateName', 'selectedPositionCode', 'selectedPositionName'];
          // if (list.some(obj => targetProps.some(prop => obj.hasOwnProperty(prop) && obj[prop]))) {
          //   setAllowClick(true);
          // }
          // 判断人选确认
          if (isConfirm == 1) {
            setAllowClick(true);
          }
          // 遍历处理每个项目
          for (let i = 0; i < list.length; i++) {
            const item = list[i];
            // 确保每个项都有id
            const id = item.code || generateUniqueId();

            // 构建基本的人员对象
            const person1: PersonInfo = {
              A00: item.candidateCode1 || '',
              A0101: item.candidateName1 || '',
              XRZW: item.presentPosition1 || '',
              extendData1: { candidateCode: '', code: '', prepareCadresCode: '' },
            };

            const person2: PersonInfo = {
              A00: item.candidateCode2 || '',
              A0101: item.candidateName2 || '',
              XRZW: item.presentPosition2 || '',
              extendData2: { candidateCode: '', code: '', prepareCadresCode: '' },
            };

            const person3: PersonInfo = {
              A00: item.candidateCode3 || '',
              A0101: item.candidateName3 || '',
              XRZW: item.presentPosition3 || '',
              extendData3: { candidateCode: '', code: '', prepareCadresCode: '' },
            };

            // 创建行项目
            const rowItem: RowItem = {
              id,
              code: item.code || '',
              type1: item.type1 || '',
              typeCode1: item.typeCode1 || '',
              type2: item.type2 || '',
              typeCode2: item.typeCode2 || '',
              // 岗位相关字段 - 平级结构
              positionCode: item.positionCode || '',
              positionName: item.positionName || '',
              jobExtend1: item.jobExtend1 || null,
              jobExtend2: item.jobExtend2 || null,
              jobExtend3: item.jobExtend3 || null,
              chooseType: item.chooseType || '',
              personConfirmValues: null,
              persons: [person1, person2, person3],
              remarksDTO: item.remarksDTO || { rmFile: '', fjFile: '', content: '' },
              sort: item.sort !== undefined ? item.sort : i, // 如果没有sort，使用索引
              candidateNameExtend1: item.candidateNameExtend1,
              candidateNameExtend2: item.candidateNameExtend2,
              candidateNameExtend3: item.candidateNameExtend3,
              CandidateSpecialList: item?.CandidateSpecialList || [],
              selectExtend: item?.selectExtend || null, // 初始化选定岗位扩展数据
              otherCandidateList: item?.otherCandidateList || [], // 加载第4-6个有关人选列表
            };

            rowItem.CandidateSpecialList =
              _isArray(rowItem.CandidateSpecialList) &&
              rowItem.CandidateSpecialList.map(item => {
                return {
                  ...item,
                  isEdit: true,
                };
              });

            if (rowItem.chooseType == '2') {
              rowItem.extend1 = item?.extend1 ?? null;
            }

            rowItem.persons[0].extendData1 = item?.extend1 ?? null;
            rowItem.persons[1].extendData2 = item?.extend2 ?? null;
            rowItem.persons[2].extendData3 = item?.extend3 ?? null;
            rowItem.extendData = item?.extend ?? null;
            rowItem.extendDto = item?.extend ?? null;
            formattedData.push(rowItem);
          }
          setAlternativeDtoList(alternativeDtoList);
          // 更新数据，删除保存数据快照的逻辑
          setData(formattedData);

          // 设置展开状态：如果行有第4-6个人选数据，自动展开
          const rowsToExpand = new Set<string>();
          formattedData.forEach(item => {
            // 检查是否为以岗找人模式且有第4-6个人选数据
            if (item.chooseType == '1' && item.otherCandidateList && item.otherCandidateList.length > 0) {
              console.log('自动展开行:', item.id, '候选人数量:', item.otherCandidateList.length);
              rowsToExpand.add(item.id);
            }
          });
          console.log('需要展开的行:', rowsToExpand);
          setExpandedRows(rowsToExpand);
        } else {
          // 如果列表为空，设置一个默认行
          const defaultData = [
            {
              id: generateUniqueId(),
              code: '',
              type1: '',
              typeCode1: '',
              type2: '',
              typeCode2: '',
              // 岗位相关字段 - 平级结构
              positionCode: '',
              positionName: '',
              chooseType: '',
              jobExtend1: null,
              jobExtend2: null,
              jobExtend3: null,
              personConfirmValues: null,
              // 创建三个空的PersonInfo对象
              persons: Array(3)
                .fill(0)
                .map(() => createEmptyPerson()),
              remarksDTO: { rmFile: '', fjFile: '', content: '' },
              sort: 0,
              otherCandidateList: [], // 初始化第4-6个有关人选列表为空数组
            },
          ];
          setData(defaultData);
          // 删除保存数据快照的逻辑
        }

        if (tableTime) {
          setCurrentTime(moment(tableTime));
        }
        setUnitName(tableUnit || '');

        // 设置汇报时间和内容
        if (reportingTime) {
          setReportingTime(moment(reportingTime));
        }
        if (reportContent) {
          setReportContent(reportContent);
        }
      } else {
        message.error('方案加载失败');
      }
    } catch (error) {
      message.error('加载失败，请重试');
      console.error('加载方案失败:', error);
    } finally {
      setLoading(false);
      // 延迟恢复自动保存，确保数据加载完全完成
      setTimeout(() => {
        setIsDataLoading(false);
      }, 500);
    }
  };

  // 准备保存数据的公共方法
  const prepareSchemeData = () => {
    // 转换数据结构成接口需要的格式
    const confirmPrepareCadres = data.map(item => {
      const {
        id,
        code,
        type1,
        typeCode1,
        type2,
        typeCode2,
        positionCode,
        positionName,
        chooseType,
        jobExtend1,
        jobExtend2,
        jobExtend3,
        personConfirmValues,
        persons,
        remarksDTO,
        sort,
        extend1,
        extend2,
        extend3,
        CandidateSpecialList = [],
        selectExtend = null,
        extendDto = {},
        otherCandidateList = [],
      } = item;

      // 处理人员信息 - 确保有3个人员对象
      const personArray = Array.isArray(persons) ? persons : [];
      const [person1 = createEmptyPerson(), person2 = createEmptyPerson(), person3 = createEmptyPerson()] = personArray;

      // 构建基础数据对象
      const baseData = {
        code: code || '',
        type1: typeof type1 === 'string' ? type1 : '',
        typeCode1: typeof typeCode1 === 'string' ? typeCode1 : '',
        type2: typeof type2 === 'string' ? type2 : '',
        typeCode2: typeof typeCode2 === 'string' ? typeCode2 : '',
        positionCode: positionCode || '',
        positionName: positionName || '',
        chooseType: chooseType || '1',
        candidateCode1: person1.A00 || '',
        candidateName1: person1.A0101 || '',
        candidateCode2: person2.A00 || '',
        candidateName2: person2.A0101 || '',
        candidateCode3: person3.A00 || '',
        candidateName3: person3.A0101 || '',
        presentPosition1: person1.XRZW || '',
        presentPosition2: person2.XRZW || '',
        presentPosition3: person3.XRZW || '',
        extendData1: person1.extendData1 || { candidateCode: '', code: '', prepareCadresCode: '' },
        extendData2: person2.extendData2 || { candidateCode: '', code: '', prepareCadresCode: '' },
        extendData3: person3.extendData3 || { candidateCode: '', code: '', prepareCadresCode: '' },
        jobExtend1: jobExtend1 || null,
        jobExtend2: jobExtend2 || null,
        jobExtend3: jobExtend3 || null,
        remarksDTO: remarksDTO || { rmFile: '', fjFile: '', content: '' },
        extendDto: extendDto,
        sort,
        CandidateSpecialList,
        selectExtend,
        otherCandidateList,
      };

      // 根据chooseType添加特定字段
      return chooseType == '2' ? baseData : { ...baseData, id };
    });
    const result: any = {
      type: null,
      confirmPrepareCadres,
      alternativeDtoList: alternativeDtoList,
      planCode: schemeId || '',
      tableTime: moment(currentTime).valueOf(),
      tableUnit: unitName.trim(),
      reportingTime: moment(reportingTime).valueOf(), // 添加汇报时间
      reportContent: reportContent.trim(), // 添加汇报内容
    };

    // 如果有拖拽类型，添加到结果中
    if (dragTypeRef.current !== undefined) {
      result.type = dragTypeRef.current;
    }

    return result;
  };

  // 处理保存后的响应
  const handleSaveResponse = (res, actionName = '保存') => {
    if (res && res.code === 0) {
      message.success(`${actionName}成功`);

      // 保存成功后重置拖拽类型
      dragTypeRef.current = undefined;

      // 如果是新建，保存后设置ID
      if (!schemeId && res.data && res.data.planCode) {
        setSchemeId(res.data.planCode);
        // 更新URL，但不刷新页面
        window.history.replaceState(null, '', `?code=${res.data.planCode}`);
      } else if (actionName === '另存为' && res.data && res.data.planCode) {
        setSchemeId(res.data.planCode);
        window.history.replaceState(null, '', `?code=${res.data.planCode}`);
      }

      // 如果服务器返回了更新后的数据，更新本地code
      if (res.data && res.data.confirmPrepareCadres && Array.isArray(res.data.confirmPrepareCadres)) {
        const savedData = res.data.confirmPrepareCadres;
        // 更新本地数据的code
        const updatedData = data.map(item => {
          // 查找对应的保存项
          const savedItem = savedData.find(saved => saved.id === item.id || (saved.code && saved.code === item.code));

          if (savedItem && savedItem.code) {
            return { ...item, code: savedItem.code };
          }
          return item;
        });

        setData(updatedData);
      } else if (schemeId) {
        // 如果没有返回更新数据，则重新加载数据
        loadScheme(schemeId);
      }
      return true;
    } else {
      message.error(`${actionName}失败`);
      return false;
    }
  };

  // 添加一个ref来保存当前的AbortController
  const saveRequestRef = useRef<AbortController | null>(null);

  // 统一保存方法，根据参数决定是否显示提示
  const saveData = async (showMessage = true, actionType = '保存') => {
    const params = prepareSchemeData();
    if (!params) return false;
    try {
      if (showMessage) setLoading(true);

      // 如果存在未完成的请求，取消它
      if (saveRequestRef.current) {
        saveRequestRef.current.abort();
      }
      // 创建新的AbortController
      const abortController = new AbortController();
      saveRequestRef.current = abortController;

      const res = await request('/api/swzzbappoint/prepareCadres/updatePrepareCadres', {
        method: 'POST',
        body: { data: params },
        signal: abortController.signal, // 添加signal
      });
      // 请求完成后清除引用
      saveRequestRef.current = null;
      if (showMessage) {
        const result = handleSaveResponse(res, actionType);
        return result;
      } else if (res && res.code === 0) {
        loadScheme(schemeId);
        return true;
      } else {
        loadScheme(schemeId);
      }
      return false;
    } catch (error) {
      if (showMessage) {
        message.error(`${actionType}失败`);
        console.error(`${actionType}方案失败:`, error);
      } else {
        console.error('自动保存失败:', error);
      }
      return false;
    } finally {
      if (showMessage) setLoading(false);
      // 确保在任何情况下都清除引用
      if (saveRequestRef.current && showMessage) {
        saveRequestRef.current = null;
      }
    }
  };

  useEffect(() => {
    if (schemeId && !isDataLoading) {
      const debouncedSave = debounce(() => {
        saveData(false);
      }, 200);

      debouncedSave();

      // 组件卸载时取消防抖和终止请求
      return () => {
        debouncedSave.cancel();
        // 如果有未完成的请求，取消它
        if (saveRequestRef.current) {
          saveRequestRef.current.abort();
          saveRequestRef.current = null;
        }
      };
    }
  }, [data, unitName, alternativeDtoList]);
  // 保存方案数据 - 手动保存按钮调用
  const saveScheme = async (type = 1) => {
    const result = await saveData(true);
    return type == 2 ? result : undefined; // 只有type=2时才返回结果
  };

  // 另存为方案
  const saveAsScheme = async () => {
    const params = prepareSchemeData();
    if (!params) return;

    try {
      setLoading(true);
      const res = await request('/api/swzzbappoint/prepareCadres/saveAsPrepareCadres', {
        method: 'POST',
        body: { data: params },
      });
      handleSaveResponse(res, '另存为');
    } catch (error) {
      message.error('操作失败，请重试');
      console.error('另存为方案失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加行
  const handleAdd = (item, index) => {
    setData(prevData => {
      // 计算新行的sort值，取当前最大sort值+1
      const maxSort = prevData.length > 0 ? Math.max(...prevData.map(item => item.sort)) : -1;
      // 确保sort值不重复，取ref中存储的值和计算出的值中的较大值
      const newSort = Math.max(maxSort, maxSortRef.current) + 1;
      // 更新ref中存储的最大sort值
      maxSortRef.current = newSort;
      // 在哪里点的添加就添加到哪里
      const oldData = [...prevData];
      // 需要插入的数据
      const crData = {
        id: generateUniqueId(),
        code: '', // 新行的code为空字符串，会在保存时由后端生成
        type1: item.type1 || '',
        typeCode1: item.typeCode1 || '',
        type2: item.type2 || '',
        typeCode2: item.typeCode2 || '',
        // 岗位相关字段 - 平级结构
        positionCode: '',
        positionName: '',
        chooseType: '',
        jobExtend1: null,
        jobExtend2: null,
        jobExtend3: null,
        personConfirmValues: null,
        // 创建三个空的PersonInfo对象
        persons: Array(3)
          .fill(0)
          .map(() => createEmptyPerson()),
        remarksDTO: { rmFile: '', fjFile: '', content: '' },
        sort: newSort, // 使用确保不重复的sort值
        alternativeDtoList: [], // 初始化备选人员列表为空数组
        jobAlternativeDtoList: [], // 初始化备选岗位列表为空数组
        selectExtend: null, // 初始化选定岗位扩展数据
      };
      oldData.splice(index + 1, 0, crData);
      return oldData;
    });
  };

  // 删除行
  const handleDelete = async id => {
    // 查找要删除的项
    const itemToDelete = data.find(item => item.id == id);
    // 显示确认对话框
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此条记录吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        let deleteSuccess = false; // 标记删除是否成功

        try {
          // 显示加载状态
          setLoading(true);

          // 如果有code，则调用API删除
          if (itemToDelete && itemToDelete.code) {
            // 调用删除API
            const res = await request(`/api/swzzbappoint/prepareCadres/deletePrepareCadres?prepareCadresCode=${itemToDelete.code}&planCode=${schemeId || ''}`, {
              method: 'GET',
            });

            if (res && res.code == 0) {
              message.success('删除成功');
              deleteSuccess = true; // 标记删除成功
            } else {
              message.error(res?.message || '删除失败');
              return; // 删除失败直接返回，不执行后续操作
            }
          } else {
            // 前端删除也标记为成功
            deleteSuccess = true;
          }

          // 只有删除成功才更新数据
          if (deleteSuccess) {
            loadScheme(schemeId);
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败，请重试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 上移
  const handleMoveUp = id => {
    const index = data.findIndex(item => item.id == id);
    if (index <= 0) return;

    setData(prevData => {
      const newData = [...prevData];
      // 交换相邻两行的sort值
      [newData[index].sort, newData[index - 1].sort] = [newData[index - 1].sort, newData[index].sort];
      // 按sort值排序并返回
      return [...newData].sort((a, b) => a.sort - b.sort);
    });
  };

  // 下移
  const handleMoveDown = id => {
    const index = data.findIndex(item => item.id == id);
    if (index == -1 || index == data.length - 1) return;

    setData(prevData => {
      const newData = [...prevData];
      // 交换相邻两行的sort值
      [newData[index].sort, newData[index + 1].sort] = [newData[index + 1].sort, newData[index].sort];
      // 按sort值排序并返回
      return [...newData].sort((a, b) => a.sort - b.sort);
    });
  };

  // 处理类型1选择
  const handleTypeChange = (value, id) => {
    if (!value || typeof value != 'object') return;

    // 暂时跳过自动保存
    setIsDataLoading(true);

    setData(prevData =>
      prevData.map(item => {
        if (item.id == id) {
          return {
            ...item,
            type1: value.CODE_NAME || '',
            typeCode1: value.CODE_VALUE || '',
          };
        }
        return item;
      })
    );

    // 延迟恢复自动保存
    setTimeout(() => {
      setIsDataLoading(false);
    }, 100);
  };

  // 处理类型2选择
  const handleType2Change = (value, id) => {
    if (!value || typeof value != 'object') return;

    // 暂时跳过自动保存
    setIsDataLoading(true);

    setData(prevData => {
      return prevData.map(item => {
        if (item.id == id) {
          return {
            ...item,
            type2: value.CODE_NAME || '',
            typeCode2: value.CODE_VALUE || '',
          };
        }
        return item;
      });
    });

    // 延迟恢复自动保存
    setTimeout(() => {
      setIsDataLoading(false);
    }, 100);
  };

  // 打开岗位选择弹窗
  const showPositionModal = obj => {
    // 查找当前行数据，获取chooseType
    const currentRow: any = data.find(item => item.id === obj.id);
    // 将平级字段转换为 position 对象结构，供 PositionSelectModal 使用
    const convertedRowData = {
      ...currentRow,
      chooseType: undefined, //岗位列打开岗位选择，需要重置chooseType
      position: {
        value: currentRow?.positionCode || '',
        label: currentRow?.positionName || '',
        chooseType: currentRow?.chooseType || '',
        jobExtend1: currentRow?.jobExtend1,
        jobExtend2: currentRow?.jobExtend2,
        jobExtend3: currentRow?.jobExtend3,
        personConfirmValues: currentRow?.personConfirmValues,
      },
    };

    setCurrentRowData(convertedRowData);
    setCurrentEditId(obj.id);
    // 根据当前行的chooseType设置
    setCurrentRowChooseType('1');
    // 不再需要设置tabType，直接根据chooseType在组件内部处理
    setPositionModalVisible(true);
  };

  // 处理岗位选择统一函数
  const handlePositionSelect = position => {
    if (!currentEditId) return;
    try {
      // 根据currentRowChooseType决定处理方式
      if (currentRowChooseType == '1') {
        // 以岗找人模式 - 更新岗位信息
        setData(prevData =>
          prevData.map(item => {
            // 只更新当前编辑的行
            if (item.id == currentEditId) {
              // 获取当前行的chooseType
              const chooseType = position.chooseType || item.chooseType || '1';

              // 提取新的jobExtend字段
              const { jobExtend1: newJobExtend1, jobExtend2: newJobExtend2, jobExtend3: newJobExtend3 } = position;

              // 保留旧的jobExtend字段（如果新的没有提供）
              let updatedJobExtend1 = newJobExtend1 !== undefined ? newJobExtend1 : item.jobExtend1;
              let updatedJobExtend2 = newJobExtend2 !== undefined ? newJobExtend2 : item.jobExtend2;
              let updatedJobExtend3 = newJobExtend3 !== undefined ? newJobExtend3 : item.jobExtend3;
              // 以人找岗清除岗位说明
              if (position.chooseType == '2') {
                updatedJobExtend1 = null;
                updatedJobExtend2 = null;
                updatedJobExtend3 = null;
              }
              return {
                ...item,
                positionCode: position.value || item.positionCode,
                positionName: position.label || item.positionName,
                chooseType, // 保存行级别的chooseType
                jobExtend1: updatedJobExtend1,
                jobExtend2: updatedJobExtend2,
                jobExtend3: updatedJobExtend3,
                personConfirmValues: position.personConfirmValues || item.personConfirmValues,
                extendDto: {
                  //人员简介
                  introduction: position.introduction,
                  currentPosition: position.XRZW,
                },
              };
            }
            return item;
          })
        );
      } else {
        // 以人找岗模式 - 更新人员信息
        setData(prevData => {
          const newData = [...prevData];
          const index = newData.findIndex(item => item.id == currentEditId);

          if (index !== -1) {
            // 获取当前行数据
            const currentRow = newData[index];
            const currentChooseType = currentRow.chooseType || '2'; // 默认为2，表示岗位选择

            // 创建persons的深拷贝
            const newPersons = [...currentRow.persons];

            // 将选择的岗位转换为人员格式并放入当前选择的索引位置
            const personIndex = currentPersonSelectIndex;
            if (personIndex < personCount) {
              newPersons[personIndex] = {
                A00: position.value || position.RECORDID || position.id,
                A0101: position.label || position.B0901A || position.A0101,
                XRZW: position.XRZW || '',
                [`extendData${personIndex + 1}`]: position.personConfirmValues,
              };
            }

            // 确保persons数组长度与personCount一致
            while (newPersons.length < personCount) {
              newPersons.push(createEmptyPerson());
            }
            // 更新当前行数据
            newData[index] = {
              ...currentRow,
              persons: newPersons,
              chooseType: currentChooseType,
              //清除岗位说明
              jobExtend1: undefined,
              jobExtend2: undefined,
              jobExtend3: undefined,
              // 保留其他字段
              personConfirmValues: position.personConfirmValues,
            };
          }

          return newData;
        });
      }
    } catch (error) {
      console.error('处理岗位选择时出错:', error);
      message.error('操作失败，请重试');
    } finally {
      // 关闭弹窗
      setPositionModalVisible(false);
    }
  };

  // 清空岗位信息
  const handlePositionDelete = id => {
    setData(prevData =>
      prevData.map(item => {
        if (item.id == id) {
          // 获取当前行数据
          const currentRow = item;

          // 判断当前行是否为以人找岗模式
          const isPersonToPosition = currentRow.chooseType == '2';

          if (isPersonToPosition) {
            // 以人找岗模式：清除岗位信息和相关的extend数据
            // 如果没有岗位信息，直接返回
            if (!currentRow.positionCode) return currentRow;

            return {
              ...currentRow,
              // 清除岗位信息
              positionCode: '',
              positionName: '',
              chooseType: '2', // 保持以人找岗模式
              // 清除jobExtend字段
              jobExtend1: null,
              jobExtend2: null,
              jobExtend3: null,
              // 清除extend数据
              extend1: undefined,
              extend2: undefined,
              extend3: undefined,
              extend: undefined,
              personConfirmValues: null,
            };
          } else {
            // 以岗找人模式：完全清除岗位信息
            return {
              ...item,
              positionCode: '',
              positionName: '',
              jobExtend1: null,
              jobExtend2: null,
              jobExtend3: null,
              personConfirmValues: null,
              // 清空岗位时也清空chooseType
              chooseType: '1', // 重置为人员选择模式
            };
          }
        }
        return item;
      })
    );
  };

  // 显示人员选择弹窗
  const showPersonSelectModal = (id: string, personIndex: number, chooseType: string = '1') => {
    // 查找当前行数据
    const rowData = data.find(item => item.id === id);

    if (!rowData) return;

    // 确保获取正确的chooseType值
    const actualChooseType = rowData.chooseType || chooseType || '1';
    const isPositionSelect = actualChooseType == '2'; // 使用==而不是===

    // 过滤已经选择的人员
    const filteredPersons: any = rowData.persons.filter(person => person.A00).map(item => item.A00);
    setFilterPersonList(filteredPersons);

    setCurrentEditId(id);
    setCurrentPersonSelectIndex(personIndex);
    setCurrentRowChooseType(actualChooseType);

    // 添加personIndex到rowData中，并转换数据结构
    const enhancedRowData = {
      ...rowData,
      personIndex, // 添加personIndex，以便PositionSelectModal知道当前操作的是第几个人员
      // 将平级字段转换为 position 对象结构
      position: {
        value: rowData.positionCode || '',
        label: rowData.positionName || '',
        chooseType: rowData.chooseType || '',
        jobExtend1: rowData.jobExtend1,
        jobExtend2: rowData.jobExtend2,
        jobExtend3: rowData.jobExtend3,
        personConfirmValues: rowData.personConfirmValues,
      },
    };
    setCurrentRowData(enhancedRowData); // 保存当前行数据

    // 根据chooseType决定显示哪个组件
    if (isPositionSelect) {
      // 显示岗位选择组件（改为单选模式）
      // 不再需要设置tabType，组件内部会根据chooseType决定显示内容
      setPositionModalVisible(true);
    } else {
      // 通过模拟点击显示弹窗
      if (personSelectButtonRef.current) {
        personSelectButtonRef.current.click();
      }
    }
  };

  // 处理人员选择确认
  const handlePersonSelectConfirm = (selecteds: EnhancedPersonType | EnhancedPersonType[]) => {
    if (!currentEditId) return;

    // 检查是否为备选人员添加模式
    if (currentEditId === 'alternative-add') {
      handleAddAlternative(selecteds);
      return;
    }

    setData(prevData => {
      const newData = [...prevData];
      const index = newData.findIndex(item => item.id == currentEditId);

      if (index != -1) {
        // 获取当前行数据
        const currentRow = newData[index];
        if (currentRow.typeCode1 == '6' || currentRow.typeCode1 == '5' || currentRow.typeCode1 == '4') {
          // 处理特殊类型的情况
          if ((currentRow.typeCode1 == '4' || currentRow.typeCode1 == '6') && Array.isArray(selecteds)) {
            // 晋升职级和干部退休的多选模式：将所有选中的人员添加到CandidateSpecialList
            const candidateData = selecteds.map(item => {
              return {
                candidateCode: item.A00,
                candidateName: item.A0101,
                introduction: item.introduction,
                currentPositionCode: item?.currentPositionCode,
                currentPosition: item.currentPosition || item.XRZW,
              };
            });
            currentRow.CandidateSpecialList = candidateData;
          } else {
            // 单选模式（干部免职或其他情况）
            const selectedPerson = Array.isArray(selecteds) ? selecteds[0] : selecteds;
            const data = {
              candidateCode: selectedPerson.A00,
              candidateName: selectedPerson.A0101,
              introduction: selectedPerson.introduction,
              currentPositionCode: selectedPerson?.currentPositionCode,
              currentPosition: selectedPerson.currentPosition || selectedPerson.XRZW,
            };
            if (currentRow?.CandidateSpecialList && currentRow?.CandidateSpecialList.length > 0) {
              currentRow.CandidateSpecialList = [...currentRow?.CandidateSpecialList, data];
            } else {
              currentRow.CandidateSpecialList = [data];
            }
          }
        }
        // 创建persons的深拷贝
        let selected: any = {};
        const newPersons = [...currentRow.persons];
        // 这里多选是数组 单选是对象
        if (Array.isArray(selecteds) && selecteds.length > 0) {
          selected = selecteds[0];
        } else {
          selected = selecteds;
        }
        // 将选择的人员填入对应位置
        if (selected && selected.A00) {
          // 有效的人员数据
          const personIndex = currentPersonSelectIndex;
          if (personIndex < 3) {
            // 前3个人选，使用原有逻辑
            // 创建扩展数据对象
            const extendData = {
              // 职级变动code
              jobChangeCode: selected.jobChangeCode || '',
              // 简述
              introduction: selected.introduction || '',
              // 职级变动
              jobChange: selected.jobChange || '',
              // 现任职务code
              currentPositionCode: selected.currentPositionCode || '',
              // 现任职务
              currentPosition: selected.currentPosition || selected.XRZW || '',
              // 拟任职务code
              proposePositionCode: selected.proposePositionCode || '',
              // 拟任职务
              proposePosition: selected.proposePosition || '',
              // 拟免职务code
              proposedRemovalPositionCode: selected.proposedRemovalPositionCode || '',
              // 拟免职务
              proposedRemovalPosition: selected.proposedRemovalPosition || selected.XRZW || '',
              // 任用理由
              reason: selected.reason || '',
            };

            // 确保不超过3个人员
            newPersons[personIndex] = {
              A00: selected.A00 || '',
              A0101: selected.A0101 || '',
              // 确保保存现任职务信息
              XRZW: selected.XRZW || selected.presentPosition || '',
              // 根据personIndex设置扩展数据字段
              extendData1: personIndex === 0 ? extendData : newPersons[personIndex].extendData1,
              extendData2: personIndex === 1 ? extendData : newPersons[personIndex].extendData2,
              extendData3: personIndex === 2 ? extendData : newPersons[personIndex].extendData3,
            };
          } else if (personIndex >= 4 && personIndex <= 6) {
            // 第4-6个人选，存储到otherCandidateList中
            const extendData = {
              jobChangeCode: selected.jobChangeCode || '',
              jobChange: selected.jobChange || '',
              currentPositionCode: selected.currentPositionCode || '',
              currentPosition: selected.currentPosition || selected.XRZW || '',
              proposePositionCode: selected.proposePositionCode || '',
              proposePosition: selected.proposePosition || '',
              proposedRemovalPositionCode: selected.proposedRemovalPositionCode || '',
              proposedRemovalPosition: selected.proposedRemovalPosition || '',
              reason: selected.reason || '',
              introduction: selected.introduction || '',
              currPerson: selected.A0101,
              jobDescription: '', // 以岗找人模式下没有岗位描述
            };

            const otherCandidate: OtherCandidateItem = {
              code: selected.A00,
              name: selected.A0101,
              index: personIndex,
              extendDto: extendData,
            };

            // 更新otherCandidateList
            const currentOtherList = [...(currentRow.otherCandidateList || [])];
            const existingIndex = currentOtherList.findIndex(item => item.index === personIndex);

            if (existingIndex >= 0) {
              // 替换现有的候选人
              currentOtherList[existingIndex] = otherCandidate;
            } else {
              // 添加新的候选人
              currentOtherList.push(otherCandidate);
            }

            // 按index排序
            currentOtherList.sort((a, b) => a.index - b.index);
            currentRow.otherCandidateList = currentOtherList;
          }
        }
        // 确保persons数组长度为3
        while (newPersons.length < 3) {
          newPersons.push(createEmptyPerson());
        }

        // 更新当前行数据
        const updatedRow = {
          ...currentRow,
          persons: newPersons,
          // 保留原有的岗位字段
        };
        newData[index] = updatedRow;
      }

      return newData;
    });
  };

  // 删除指定人员：以岗找人时移入备选人员列表，以人找岗时直接清空
  const handlePersonDelete = (item: RowItem, personIndex: number) => {
    const currentPerson: any = item.persons[personIndex];

    // 只有以岗找人模式（chooseType == '1'）才将人员移入备选人员列表
    if (item.chooseType == '1' && currentPerson) {
      // 获取人员的扩展数据
      const personExtendData = currentPerson[`extendData${personIndex + 1}`];

      // 创建备选人员对象
      const alternativeDto: AlternativeDto = {
        code: currentPerson.A00,
        name: currentPerson.A0101,
        extendDto: {
          jobChangeCode: personExtendData?.jobChangeCode || '',
          jobChange: personExtendData?.jobChange || '',
          currentPositionCode: personExtendData?.currentPositionCode || '',
          currentPosition: currentPerson.XRZW || personExtendData?.currentPosition || '',
          proposePositionCode: personExtendData?.proposePositionCode || '',
          proposePosition: personExtendData?.proposePosition || '',
          proposedRemovalPositionCode: personExtendData?.proposedRemovalPositionCode || '',
          proposedRemovalPosition: personExtendData?.proposedRemovalPosition || '',
          reason: personExtendData?.reason || '',
          introduction: personExtendData?.introduction || '',
          currPerson: currentPerson.A0101,
          jobDescription: '', // 以岗找人模式下没有岗位描述
        },
      };

      // 检查备选人员列表中是否已存在该人员
      setAlternativeDtoList(prevList => {
        const existingCodes = prevList.map(alt => alt.code);
        if (currentPerson.A00 && !existingCodes.includes(currentPerson.A00)) {
          message.success(`已将 ${currentPerson.A0101} 移入备选人员列表`);
          return [...prevList, alternativeDto];
        }
        return prevList;
      });
    }

    // 更新数据，清空特定索引的人员
    setData(prevData => {
      const newData = [...prevData];
      const index = newData.findIndex(row => row.id == item.id);

      if (index != -1) {
        // 创建persons的深拷贝并清空特定索引的人员
        const newPersons = [...newData[index].persons];
        newPersons[personIndex] = createEmptyPerson();

        // 更新行数据
        const updatedRow = {
          ...newData[index],
          persons: newPersons,
        };

        // 如果删除的是对应索引的人员，则清除相应的jobExtend字段
        if (updatedRow.chooseType == '2') {
          // 根据personIndex清除对应的jobExtend字段（只支持前3个）
          if (personIndex === 0 && newData[index].jobExtend1) {
            delete updatedRow.jobExtend1;
          } else if (personIndex === 1 && newData[index].jobExtend2) {
            delete updatedRow.jobExtend2;
          } else if (personIndex === 2 && newData[index].jobExtend3) {
            delete updatedRow.jobExtend3;
          }
          // 注意：jobExtend只支持前3个人员，第4-6个人员没有对应的jobExtend字段
        }

        newData[index] = updatedRow;
      }

      return newData;
    });
  };

  // 删除其他候选人
  const handleDeleteOtherCandidate = (itemId: string, candidateIndex: number) => {
    setData(prevData => {
      return prevData.map(item => {
        if (item.id === itemId) {
          const newOtherCandidateList = [...(item.otherCandidateList || [])];

          // 获取要删除的候选人信息
          const candidateToDelete = newOtherCandidateList[candidateIndex];

          // 如果是以岗找人模式且候选人存在，将其移入备选人员列表
          if (item.chooseType == '1' && candidateToDelete) {
            const alternativeDto: AlternativeDto = {
              code: candidateToDelete.code || '',
              name: candidateToDelete.name || '',
              extendDto: {
                jobChangeCode: candidateToDelete.extendDto?.jobChangeCode || '',
                jobChange: candidateToDelete.extendDto?.jobChange || '',
                currentPositionCode: candidateToDelete.extendDto?.currentPositionCode || '',
                currentPosition: candidateToDelete.extendDto?.currentPosition || '',
                proposePositionCode: candidateToDelete.extendDto?.proposePositionCode || '',
                proposePosition: candidateToDelete.extendDto?.proposePosition || '',
                proposedRemovalPositionCode: candidateToDelete.extendDto?.proposedRemovalPositionCode || '',
                proposedRemovalPosition: candidateToDelete.extendDto?.proposedRemovalPosition || '',
                reason: candidateToDelete.extendDto?.reason || '',
                introduction: candidateToDelete.extendDto?.introduction || '',
                currPerson: candidateToDelete.name || '',
                jobDescription: candidateToDelete.extendDto?.jobDescription || '',
              },
            };

            // 检查备选人员列表中是否已存在该人员
            setAlternativeDtoList(prevList => {
              const existingCodes = prevList.map(alt => alt.code);
              if (candidateToDelete.code && !existingCodes.includes(candidateToDelete.code)) {
                message.success(`已将 ${candidateToDelete.name} 移入备选人员列表`);
                return [...prevList, alternativeDto];
              }
              return prevList;
            });
          }

          // 删除候选人
          newOtherCandidateList.splice(candidateIndex, 1);
          return {
            ...item,
            otherCandidateList: newOtherCandidateList,
          };
        }
        return item;
      });
    });
  };

  // 切换第二行显示
  const toggleRowExpansion = (itemId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  // 打开备注弹窗
  const handleOpenRemarkModal = item => {
    const currentItem = item;
    setCurrentRemarkId(item.id);
    setRemarkModalVisible(true);

    // 获取现有的备注数据（现在是对象）
    let remarkData = { rmFile: '', fjFile: '', content: '' };
    if (currentItem?.remarksDTO) {
      remarkData = {
        rmFile: (currentItem.remarksDTO as any).rmFile || '',
        fjFile: (currentItem.remarksDTO as any).fjFile || '',
        content: (currentItem.remarksDTO as any).content || '',
      };
    }

    // 设置表单值
    remarkForm.setFieldsValue({ content: remarkData.content });

    // 设置文件列表用于Upload组件回显
    const rmFiles = remarkData.rmFile
      ? [
          {
            uid: '-1',
            name: remarkData.rmFile.split('/').pop() || '任免文件',
            status: 'done',
            url: remarkData.rmFile,
          },
        ]
      : [];

    const fjFiles = remarkData.fjFile
      ? [
          {
            uid: '-2',
            name: remarkData.fjFile.split('/').pop() || '附件文件',
            status: 'done',
            url: remarkData.fjFile,
          },
        ]
      : [];

    setRmFileList(rmFiles);
    setFjFileList(fjFiles);
  };

  // 关闭备注弹窗
  const handleCloseRemarkModal = () => {
    setRemarkModalVisible(false);
    setCurrentRemarkId(null);
    remarkForm.resetFields();
    setRmFileList([]);
    setFjFileList([]);
  };

  // 保存备注
  const handleSaveRemark = async () => {
    try {
      const values = await remarkForm.validateFields();

      // 从文件列表中获取文件URL
      const rmFileUrl = rmFileList.length > 0 ? rmFileList[0].url || rmFileList[0].response?.data?.[0]?.url || '' : '';
      const fjFileUrl = fjFileList.length > 0 ? fjFileList[0].url || fjFileList[0].response?.data?.[0]?.url || '' : '';

      const remarkData = {
        rmFile: rmFileUrl,
        fjFile: fjFileUrl,
        content: values.content || '',
      };

      // 更新数据（现在remark是对象）
      setData(prevData =>
        prevData.map(item => {
          if (item.id === currentRemarkId) {
            return { ...item, remarksDTO: remarkData };
          }
          return item;
        })
      );

      handleCloseRemarkModal();
      message.success('备注保存成功');
    } catch (error) {
      console.error('保存备注失败:', error);
    }
  };

  // 删除方案级别的备选人员
  const handleDeleteAlternative = (altIndex: number) => {
    setAlternativeDtoList(prevList => {
      const newList = [...prevList];
      newList.splice(altIndex, 1);
      return newList;
    });
  };

  // 处理新增备选人员
  const handleAddAlternative = (selectedPersons: EnhancedPersonType | EnhancedPersonType[]) => {
    const persons = Array.isArray(selectedPersons) ? selectedPersons : [selectedPersons];

    // 过滤已存在的备选人员，避免重复添加
    const existingCodes = alternativeDtoList.map(alt => alt.code);
    const newPersons = persons.filter(person => !existingCodes.includes(person.A00 || ''));

    if (newPersons.length === 0) {
      message.warning('选择的人员已在备选列表中，请选择其他人员');
      return;
    }

    // 转换为AlternativeDto格式并添加到备选列表
    const newAlternatives: AlternativeDto[] = newPersons.map(person => ({
      code: person.A00 || '',
      name: person.A0101 || '',
      extendDto: {
        jobChangeCode: person.jobChangeCode || '',
        jobChange: person.jobChange || '',
        currentPositionCode: person.currentPositionCode || '',
        currentPosition: person.currentPosition || person.XRZW || '',
        proposePositionCode: person.proposePositionCode || '',
        proposePosition: person.proposePosition || '',
        proposedRemovalPositionCode: person.proposedRemovalPositionCode || '',
        proposedRemovalPosition: person.proposedRemovalPosition || '',
        reason: person.reason || '',
        introduction: person.introduction || '',
        currPerson: person.A0101 || '',
        jobDescription: '',
      },
    }));

    setAlternativeDtoList(prevList => [...prevList, ...newAlternatives]);
    message.success(`已添加 ${newPersons.length} 个备选人员`);
  };

  // 处理从备选列拖拽到人员位置
  const handleAlternativeDrop = (dragData: any, targetItemId: string, targetIndex: number) => {
    const { subType, sourceId, sourceIndex } = dragData;

    // 只处理人员备选拖拽，岗位备选已废除
    if (subType !== 'person') {
      console.log('拖拽类型不是person:', subType);
      message.warning('岗位拖拽功能已废除');
      return;
    }
    // 先检查目标是否有效
    const currentTargetItem = data.find(item => item.id === targetItemId);
    if (!currentTargetItem) {
      console.log('目标项不存在');
      return;
    }

    // 检查是否为以人找岗模式
    if (currentTargetItem.chooseType == '2') {
      message.warning('不能将备选人员拖拽到以人选岗人员位置');
      return;
    }

    // 只有以岗找人模式才继续处理
    if (currentTargetItem.chooseType == '1') {
      setData(prevData => {
        const newData = [...prevData];
        const targetItem = newData.find(item => item.id === targetItemId);
        if (!targetItem) return prevData;

        // 处理前3个人选的拖拽
        if (targetIndex < 3) {
          const targetPerson = targetItem.persons[targetIndex];

          // 如果目标位置已有人员，将其移到方案级别的备选中
          if (targetPerson && targetPerson.A0101) {
            // 获取目标人员的扩展数据
            const targetPersonExtendData = targetIndex === 0 ? targetPerson.extendData1 : targetIndex === 1 ? targetPerson.extendData2 : targetPerson.extendData3;

            const alternativeDto: AlternativeDto = {
              code: targetPerson.A00 || '',
              name: targetPerson.A0101,
              extendDto: {
                jobChangeCode: targetPersonExtendData?.jobChangeCode || '',
                jobChange: targetPersonExtendData?.jobChange || '',
                currentPositionCode: targetPersonExtendData?.currentPositionCode || '',
                currentPosition: targetPersonExtendData?.currentPosition || '',
                proposePositionCode: targetPersonExtendData?.proposePositionCode || targetItem.positionCode || '',
                proposePosition: targetPersonExtendData?.proposePosition || targetItem.positionName || '',
                proposedRemovalPositionCode: targetPersonExtendData?.proposedRemovalPositionCode || '',
                proposedRemovalPosition: targetPersonExtendData?.proposedRemovalPosition || '',
                reason: targetPersonExtendData?.reason || '',
                introduction: targetPersonExtendData?.introduction || '',
                currPerson: targetPerson.A0101,
                jobDescription: (targetIndex === 0 ? targetItem.jobExtend1 : targetIndex === 1 ? targetItem.jobExtend2 : targetItem.jobExtend3)?.jobDescription || '',
              },
            };
            // 添加到方案级别的备选列表
            setAlternativeDtoList(prevList => [...prevList, alternativeDto]);
          }
          let data = dragData.data;
          // 创建新的人员对象，包含完整的扩展数据
          let newPerson: PersonInfo = {
            A00: data.code,
            A0101: data.name,
            XRZW: data.extendDto?.currentPosition || '',
          };
          // 将扩展数据映射到对应位置
          if (targetIndex === 0) {
            newPerson.extendData1 = {
              jobChangeCode: data.extendDto?.jobChangeCode || '',
              jobChange: data.extendDto?.jobChange || '',
              currentPositionCode: data.extendDto?.currentPositionCode || '',
              currentPosition: data.extendDto?.currentPosition || '',
              proposePositionCode: data.extendDto?.proposePositionCode || '',
              proposePosition: data.extendDto?.proposePosition || '',
              proposedRemovalPositionCode: data.extendDto?.proposedRemovalPositionCode || '',
              proposedRemovalPosition: data.extendDto?.proposedRemovalPosition || '',
              reason: data.extendDto?.reason || '',
              introduction: data.extendDto?.introduction || '',
            };
          } else if (targetIndex === 1) {
            newPerson.extendData2 = {
              jobChangeCode: data.extendDto?.jobChangeCode || '',
              jobChange: data.extendDto?.jobChange || '',
              currentPositionCode: data.extendDto?.currentPositionCode || '',
              currentPosition: data.extendDto?.currentPosition || '',
              proposePositionCode: data.extendDto?.proposePositionCode || '',
              proposePosition: data.extendDto?.proposePosition || '',
              proposedRemovalPositionCode: data.extendDto?.proposedRemovalPositionCode || '',
              proposedRemovalPosition: data.extendDto?.proposedRemovalPosition || '',
              reason: data.extendDto?.reason || '',
              introduction: data.extendDto?.introduction || '',
            };
          } else if (targetIndex === 2) {
            newPerson.extendData3 = {
              jobChangeCode: data.extendDto?.jobChangeCode || '',
              jobChange: data.extendDto?.jobChange || '',
              currentPositionCode: data.extendDto?.currentPositionCode || '',
              currentPosition: data.extendDto?.currentPosition || '',
              proposePositionCode: data.extendDto?.proposePositionCode || '',
              proposePosition: data.extendDto?.proposePosition || '',
              proposedRemovalPositionCode: data.extendDto?.proposedRemovalPositionCode || '',
              proposedRemovalPosition: data.extendDto?.proposedRemovalPosition || '',
              reason: data.extendDto?.reason || '',
              introduction: data.extendDto?.introduction || '',
            };
          }

          // 将新人员放到目标位置
          targetItem.persons[targetIndex] = newPerson;
        }
        // 处理第4-6个人选的拖拽
        else if (targetIndex >= 4 && targetIndex <= 6) {
          const newOtherCandidateList = [...(targetItem.otherCandidateList || [])];

          // 查找目标位置是否已有候选人
          const existingCandidateIndex = newOtherCandidateList.findIndex(c => c.index === targetIndex);

          // 如果目标位置已有候选人，将其移到方案级别的备选中
          if (existingCandidateIndex >= 0) {
            const existingCandidate = newOtherCandidateList[existingCandidateIndex];
            const alternativeDto: AlternativeDto = {
              code: existingCandidate.code || '',
              name: existingCandidate.name || '',
              extendDto: {
                ...existingCandidate.extendDto,
                proposePositionCode: targetItem.positionCode || '',
                proposePosition: targetItem.positionName || '',
              },
            };
            // 添加到方案级别的备选列表
            setAlternativeDtoList(prevList => [...prevList, alternativeDto]);

            // 移除现有候选人
            newOtherCandidateList.splice(existingCandidateIndex, 1);
          }

          let data = dragData.data;
          // 创建新的候选人对象
          const newCandidate: OtherCandidateItem = {
            code: data.code,
            name: data.name,
            index: targetIndex,
            extendDto: {
              jobChangeCode: data.extendDto?.jobChangeCode || '',
              jobChange: data.extendDto?.jobChange || '',
              currentPositionCode: data.extendDto?.currentPositionCode || '',
              currentPosition: data.extendDto?.currentPosition || '',
              proposePositionCode: targetItem.positionCode || '',
              proposePosition: targetItem.positionName || '',
              proposedRemovalPositionCode: data.extendDto?.proposedRemovalPositionCode || '',
              proposedRemovalPosition: data.extendDto?.proposedRemovalPosition || '',
              reason: data.extendDto?.reason || '',
              introduction: data.extendDto?.introduction || '',
              currPerson: data.name,
              jobDescription: '',
            },
          };

          // 添加新候选人并排序
          newOtherCandidateList.push(newCandidate);
          newOtherCandidateList.sort((a, b) => a.index - b.index);

          // 更新候选人列表
          targetItem.otherCandidateList = newOtherCandidateList;
        }
        return newData;
      });

      // 只有在以岗找人模式下成功处理后才从备选列表中移除
      if (sourceId === 'global') {
        setAlternativeDtoList(prevList => prevList.filter((_, index) => index !== sourceIndex));
      }
    }
  };

  // 处理制表单位修改
  const handleUnitNameChange = e => {
    setUnitName(e.target.value);
  };

  // 加载版本历史记录
  const loadVersionHistory = async () => {
    if (!schemeId) {
      message.warning('请先保存方案后再查看版本历史');
      return;
    }

    try {
      setVersionLoading(true);

      const res = await request(`/api/swzzbappoint/prepareCadres/findPrepareCadresHistory?planCode=${schemeId}`);

      if (res && res.code === 0 && res.data) {
        // 处理新的数据结构
        const versionData: VersionHistoryItem[] = [];
        const rowsToExpand = expandedRows;
        // 遍历对象的所有key (如 V1.0, V2.0, V3.0)
        Object.keys(res.data).forEach((version, vIndex) => {
          // 获取该版本的记录列表
          const records = res.data[version] || [];
          if (records.length > 0) {
            records.forEach(item => {
              if (item.otherCandidateList?.length > 0) {
                rowsToExpand.add(item.code);
              }
            });

            // 使用第一条记录的信息作为版本信息
            const firstRecord = records[0];
            versionData.push({
              id: firstRecord.planCode, // 使用planCode作为ID
              version: version,
              createTime: firstRecord.createTime,
              tableTime: firstRecord.createTime, // 使用createTime作为tableTime
              tableUnit: '', // 版本记录中可能没有tableUnit
              versionDesc: `版本${version}`, // 自动生成版本说明
              createUser: '', // 版本记录中可能没有createUser
              records: records, // 保存该版本的所有记录
              key: `${version}_${vIndex}`, // 生成唯一key
              index: vIndex + 1, // 序号
            });
          }
        });
        setExpandedRows(rowsToExpand);
        // 按版本号排序（从新到旧）
        versionData.sort((a, b) => {
          // 提取版本号数字部分进行比较
          const versionA = parseFloat(a.version.replace('V', ''));
          const versionB = parseFloat(b.version.replace('V', ''));
          return versionB - versionA;
        });

        setVersionList(versionData);
      } else {
        setVersionList([]);
        message.warning('暂无版本历史记录');
      }
    } catch (error) {
      console.error('加载版本历史失败:', error);
      message.error('加载版本历史失败');
    } finally {
      setVersionLoading(false);
    }
  };

  // 应用指定版本
  const applyVersion = async (versionData: VersionHistoryItem) => {
    // 如果没有版本数据或记录为空，显示错误并返回
    if (!versionData?.records?.length) {
      message.error('版本数据加载失败');
      return;
    }

    try {
      const res = await applyThisVersion({
        planCode: schemeId,
        version: versionData.version,
      });
      if (res.code == 0) {
        message.success(`已加载${versionData.version}版本`);
        setVersionModalVisible(false);
        setLoading(false);
        loadScheme(schemeId);
      }
    } catch (error) {
      message.error('加载历史版本失败');
    }
  };

  // 显示版本管理弹窗
  const showVersionModal = () => {
    setVersionModalVisible(true);
    loadVersionHistory();
  };

  // 跳转到共享方案页面 改为 在当前页面弹窗显示共享方案的内容
  const showSharedPlansModal = () => {
    setFaVisible(true);
  };

  // 处理人选确认操作
  const handleResultConfirm = () => {
    if (!schemeId) {
      message.warning('请先保存方案');
      return;
    }

    // 拷贝数据用于弹窗
    const copiedData = JSON.parse(JSON.stringify(data));
    setResultConfirmData(copiedData);
    setResultConfirmVisible(true);
  };

  // 提交人选确认
  const submitResultConfirm = async () => {
    if (!schemeId || resultConfirmData.length === 0) {
      return;
    }

    try {
      setResultConfirmLoading(true);

      interface confirmData1 {
        code: string;
        selectedPositionCode: string;
        selectedPositionName: string;
        selectedCandidateCode: string;
        selectedCandidateName: string;
      }
      interface confirmData2 {
        code: string;
        CandidateSpecialList: {
          candidateCode?: string;
          candidateName?: string;
          currentPosition?: string;
          introduction?: string;
          isChecked?: boolean;
          isEdit?: boolean;
        }[];
      }
      // 准备人选确认数据
      const confirmData: (confirmData1 | confirmData2)[] = [];

      // 从拷贝的数据中生成确认数据
      for (const item of resultConfirmData) {
        // if (!item.code || !item.positionCode) continue; //以前用于判断3个特殊值类型
        if (!item.code) continue;

        // 计算有效人员数量
        const validPersons = item.persons.filter(p => p && p.A00 && p.isChecked);

        // 校验：普通人员只能有一个人
        const specials = item.typeCode1 == '6' || item.typeCode1 == '5' || item.typeCode1 == '4';
        if (!specials && validPersons.length !== 1) {
          message.warning(`岗位【${item.positionName}】需要且只能有一个人员被选择`);
          return;
        }
        if (specials) {
          confirmData.push({
            code: item.code,
            CandidateSpecialList: item.CandidateSpecialList.map(item => {
              return {
                ...item,
                isChoose: item.isChecked ? 1 : 0,
              };
            }),
            // CandidateSpecialList: item.CandidateSpecialList.filter((item) => {
            //   return item.isChecked
            // }).map((item) => {
            //   return {
            //     ...item,
            //     isChoose: true
            //   }
            // })
          });
        }
        // 添加到结果数据中
        for (const person of validPersons) {
          // chooseType等于2的时候 需要交换人员信息
          if (item?.chooseType == '2') {
            confirmData.push({
              code: item.code,
              // 岗位信息
              selectedPositionCode: person.A00 || '',
              selectedPositionName: person.A0101 || '',
              // 人员信息
              selectedCandidateCode: item.positionCode || '',
              selectedCandidateName: item.positionName || '',
            });
          } else {
            let isJson = false;
            let zdyobj: any = [];
            try {
              zdyobj = JSON.parse(person.A0101);
              isJson = true;
            } catch (error) {
              isJson = false;
            }
            confirmData.push({
              code: item.code,
              // 岗位信息
              selectedPositionCode: item.positionCode || '',
              selectedPositionName: item.positionName || '',
              // 人员信息
              selectedCandidateCode: isJson ? zdyobj.map(item => item.candidateCode)?.join(',') : person.A00 || '',
              selectedCandidateName: isJson ? zdyobj.map(item => item.candidateName)?.join(',') : person.A0101,
            });
          }
        }
      }
      // 使用正确的参数格式提交
      const res = await request('/api/swzzbappoint/prepareCadres/resConfirm', {
        method: 'POST',
        body: {
          data: {
            confirmPrepareCadres: confirmData,
            planCode: schemeId,
          },
        },
      });

      if (res && res.code === 0) {
        message.success('人选确认成功');
        setResultConfirmVisible(false);
        loadScheme(schemeId);
      } else {
        message.error(res?.message || '人选确认失败');
      }
    } catch (error) {
      console.error('人选确认失败:', error);
      message.error('人选确认失败，请重试');
    } finally {
      setResultConfirmLoading(false);
    }
  };

  // 表格头部渲染函数
  const renderCols = () => {
    return new Array(7).fill('').map((item, index) => {
      if (index < 2) {
        return <col key={index} style={{ width: 120 }} />;
      }
      if (index > 2 && index < 6) {
        return <col key={index} style={{ width: 200 }} />;
      }

      return <col key={index} style={{ width: 150 }} />;
    });
  };

  const getWidth = () => {
    return renderCols().reduce((all, it) => {
      return all + it?.props?.style?.width || 0;
    }, 30);
  };

  const WIDTH = useMemo(() => getWidth(), [getWidth]);

  const planName = _history.location.query.planName;

  // 使用useMemo优化表格头定义
  const tableHead = useMemo(
    () => [
      {
        key: '02',
        name: '类型',
        parent: '-1',
        colSpan: 2,
      },
      {
        key: '03',
        name: '岗位',
        parent: '-1',
      },
      {
        key: '04',
        name: '有关人选',
        parent: '-1',
        colSpan: 3,
      },
      {
        key: '05',
        name: '备注',
        parent: '-1',
      },
    ],
    []
  );

  const handleConfirmCancel = () => {
    setConfirmModalVisible(false);
  };
  const handleConfirmInfoSubmit = values => {
    let arr: RowItem[] = [];
    if (currentRowChooseType == '1') {
      arr = data.map(item => {
        if (item.id === currentEditId) {
          return {
            ...item,
            persons: item.persons.map((i, index) => {
              if (index == currentPersonSelectIndex) {
                return {
                  ...i,
                  [`extendData${index + 1}`]: {
                    ...i[`extendData${index + 1}`],
                    ...values,
                  },
                };
              } else {
                return i;
              }
            }),
          };
        } else {
          return item;
        }
      });
    } else {
      arr = data.map(item => {
        if (item.id === currentEditId) {
          return {
            ...item,
            extend1: values,
          };
        } else {
          return item;
        }
      });
    }
    setData(arr);

    setConfirmModalVisible(false);
  };

  const openConfirmInfoModal = (id, index, chooseType) => {
    // 查找当前行数据
    const rowData = data.find(item => item.id === id);
    if (!rowData) return;
    setCurrentRowData(rowData); // 保存当前行数据
    const extendData = rowData.persons[index];
    setCurrentEditId(id);
    setCurrentPersonSelectIndex(index);
    setCurrentRowChooseType(chooseType);
    setConfirmModalData({
      A0101: extendData.A0101,
      ...extendData[`extendData${index + 1}`],
    });

    setConfirmModalVisible(true);
  };
  //岗位弹框修改 以人找岗的时候 并且有值的时候需要弹出人员确认信息
  const changeShowPositionModal = item => {
    if (item.typeCode1 == '6' || item.typeCode1 == '5' || item.typeCode1 == '4') {
      return;
    }
    // if (item.chooseType == '2' && item.positionName && item.positionCode) {
    //   setCurrentEditId(item.id);
    //   setConfirmModalData({
    //     A0101: item.positionName,
    //     ...item.extend1,
    //   });
    //   setCurrentRowChooseType(item.chooseType);
    //   setConfirmModalVisible(true);
    // } else {
    //   showPositionModal(item);
    // }
    showPositionModal(item);
  };

  // 选择框选择的时候 调用函数
  const handleSelectPositingChange = (value, id) => {
    setData(prevData =>
      prevData.map(item => {
        if (item.id == id) {
          return {
            ...item,
            positionCode: value || '',
            positionName: value || '',
          };
        }
        return item;
      })
    );
  };

  const delectCandidateSpecialListRow = (index, id) => {
    setData(prevData =>
      prevData.map(item => {
        if (item.id == id) {
          return {
            ...item,
            CandidateSpecialList: item.CandidateSpecialList.filter((_, i) => i != index),
          };
        }
        return item;
      })
    );
  };
  const editCandidateSpecialListRow = (index, id) => {
    setIsDataLoading(true);
    setData(prevData =>
      prevData.map(item => {
        if (item.id == id) {
          return {
            ...item,
            CandidateSpecialList:
              _isArray(item.CandidateSpecialList) &&
              item.CandidateSpecialList.map((citem, cindex) => {
                if (cindex == index) {
                  return {
                    ...citem,
                    isEdit: false,
                  };
                }
                return citem;
              }),
          };
        }
        return item;
      })
    );
    setTimeout(() => {
      setIsDataLoading(false);
    }, 500);
  };
  // 当干部退休  干部免值 晋升职级  修改描述的方法
  const handleIntroductionChange = (value, id, index) => {
    setData(prevData =>
      prevData.map(item => {
        if (item.id == id) {
          return {
            ...item,
            CandidateSpecialList:
              _isArray(item.CandidateSpecialList) &&
              item.CandidateSpecialList.map((citem, cindex) => {
                if (cindex == index) {
                  return {
                    ...citem,
                    isEdit: true,
                    introduction: value,
                  };
                }
                return citem;
              }),
          };
        }
        return item;
      })
    );
  };

  // 删除人员的函数（用于只读模式）
  const handleDeletePersonInReadOnly = (itemId: string, personIndex: number, onDataChange?: React.Dispatch<React.SetStateAction<any[]>>) => {
    if (!onDataChange) return;

    onDataChange(prevData =>
      prevData.map(item => {
        if (item.id === itemId) {
          const newPersons = [...item.persons];
          newPersons[personIndex] = {}; // 清空该位置的人员数据
          return {
            ...item,
            persons: newPersons,
          };
        }
        return item;
      })
    );
  };

  // 在人选确认模式下处理勾选操作
  const handleCheckPersonInResultConfirm = (itemId: string, personIndex: string, onDataChange?: React.Dispatch<React.SetStateAction<any[]>>) => {
    if (!onDataChange) return;
    onDataChange(prevData => {
      return prevData.map(item => {
        if (item.persons && item.id === itemId) {
          const newPersons = item.persons.map((person: any) => {
            if (person) {
              // 如果是当前点击的人员，切换选中状态；其他人员取消选中
              return {
                ...person,
                isChecked: person.A00 == personIndex ? !person.isChecked : false,
              };
            }
            return person;
          });

          return {
            ...item,
            persons: newPersons,
          };
        }
        return item;
      });
    });
  };

  // 3种特殊值的有关人选修改
  const handleSpecialCheckBox = (rowId: string, index: number, item: any, value, onDataChange) => {
    if (!onDataChange) return;
    console.log('🚀 ~ handleSpecialCheckBox ~ value:', value);
    console.log('🚀 ~ handleSpecialCheckBox ~ item:', item);
    console.log('🚀 ~ handleSpecialCheckBox ~ index:', index);
    console.log('🚀 ~ handleSpecialCheckBox ~ rowId:', rowId);
    onDataChange(prevData => {
      return prevData.map(item => {
        if (item.persons && item.id === rowId) {
          const newCandidateSpecialList = item.CandidateSpecialList.map((person: any, personIndex) => {
            if (person && index == personIndex) {
              // 如果是当前点击的人员，切换选中状态；其他人员取消选中
              return {
                ...person,
                isChecked: value,
              };
            }
            return person;
          });

          return {
            ...item,
            CandidateSpecialList: newCandidateSpecialList,
          };
        }
        return item;
      });
    });
  };

  // 处理跨行人员拖拽交换位置
  const handleCrossRowDragSwap = (fromItemId: string, fromIndex: number, toItemId: string, toIndex: number) => {
    // 设置拖拽类型为跨行拖拽
    dragTypeRef.current = 2;

    setData(prevData => {
      // 获取源人员和目标人员信息
      const sourceItem = prevData.find(item => item.id === fromItemId);
      const targetItem = prevData.find(item => item.id === toItemId);

      if (!sourceItem || !targetItem) return prevData;

      // 检查是否为相同类型的行，只有相同类型才允许跨行拖拽
      if (sourceItem.chooseType !== targetItem.chooseType) {
        message.warning('只能在相同类型的行之间进行拖拽交换');
        return prevData;
      }

      // 以人找岗模式不允许跨行拖拽
      if (sourceItem.chooseType == '2') {
        message.warning('以人找岗模式下不允许跨行拖拽');
        return prevData;
      }

      // 处理前3个人选的跨行拖拽
      if (fromIndex < 3 && toIndex < 3) {
        const fromPerson = sourceItem.persons[fromIndex];
        const toPerson = targetItem.persons[toIndex];

        // 获取扩展数据的内部函数
        const getExtendData = (person: PersonInfo, index: number) => {
          if (index === 0) return person.extendData1;
          if (index === 1) return person.extendData2;
          if (index === 2) return person.extendData3;
          return null;
        };

        return prevData.map(item => {
          if (item.id === fromItemId) {
            // 处理源行 - 移除源位置的人员
            const newPersons = [...item.persons];
            newPersons[fromIndex] = createEmptyPerson();

            // 处理jobExtend字段
            let updatedJobExtend1 = item.jobExtend1;
            let updatedJobExtend2 = item.jobExtend2;
            let updatedJobExtend3 = item.jobExtend3;

            if (item.chooseType == '2') {
              // 以人找岗模式：清除jobExtend字段，因为岗位说明跟随人员移动
              if (fromIndex === 0) updatedJobExtend1 = null;
              else if (fromIndex === 1) updatedJobExtend2 = null;
              else if (fromIndex === 2) updatedJobExtend3 = null;
            }

            return {
              ...item,
              persons: newPersons,
              jobExtend1: updatedJobExtend1,
              jobExtend2: updatedJobExtend2,
              jobExtend3: updatedJobExtend3,
            };
          } else if (item.id === toItemId) {
            // 处理目标行
            const newPersons = [...item.persons];

            if (fromPerson && fromPerson.A0101) {
              // 只有在以岗找人模式下，且目标位置有人员时，才将其移到方案级别的备选中
              if (toPerson && toPerson.A0101 && item.chooseType == '1') {
                // 获取目标人员的扩展数据
                const targetPersonExtendData = getExtendData(toPerson, toIndex);
                const jobDescription = (toIndex === 0 ? item.jobExtend1 : toIndex === 1 ? item.jobExtend2 : item.jobExtend3)?.jobDescription || '';

                const alternativeDto = {
                  code: toPerson.A00 || '',
                  name: toPerson.A0101 || '',
                  extendDto: {
                    jobChangeCode: targetPersonExtendData?.jobChangeCode || '',
                    jobChange: targetPersonExtendData?.jobChange || '',
                    currentPositionCode: targetPersonExtendData?.currentPositionCode || '',
                    currentPosition: targetPersonExtendData.currentPosition || toPerson.XRZW || '',
                    proposePositionCode: targetPersonExtendData?.proposePositionCode || item.positionCode || '',
                    proposePosition: targetPersonExtendData?.proposePosition || item.positionName || '',
                    proposedRemovalPositionCode: targetPersonExtendData?.proposedRemovalPositionCode || '',
                    proposedRemovalPosition: targetPersonExtendData?.proposedRemovalPosition || '',
                    reason: targetPersonExtendData?.reason || '',
                    introduction: targetPersonExtendData?.introduction || '',
                    currPerson: toPerson.A0101 || '',
                    jobDescription,
                  },
                };
                // 添加到方案级别的备选列表
                setAlternativeDtoList(prevList => [...prevList, alternativeDto]);
              }

              // 创建新的人员对象，重新映射扩展数据字段
              const newPerson: PersonInfo = {
                A00: fromPerson.A00,
                A0101: fromPerson.A0101,
                XRZW: fromPerson.XRZW,
              };

              // 获取源人员的扩展数据并重新映射到目标位置
              const fromOriginalExtendData = getExtendData(fromPerson, fromIndex);

              console.log(item, 'newPersons');
              // 拖动后替换岗位相关信息
              fromOriginalExtendData.proposePositionCode = item.positionCode;
              fromOriginalExtendData.proposePosition = item.positionName;

              if (toIndex === 0) newPerson.extendData1 = fromOriginalExtendData;
              else if (toIndex === 1) newPerson.extendData2 = fromOriginalExtendData;
              else if (toIndex === 2) newPerson.extendData3 = fromOriginalExtendData;

              newPersons[toIndex] = newPerson;

              // 处理jobExtend字段
              let updatedJobExtend1 = item.jobExtend1;
              let updatedJobExtend2 = item.jobExtend2;
              let updatedJobExtend3 = item.jobExtend3;

              if (item.chooseType == '2') {
                // 以人找岗模式：岗位说明跟随人员移动
                if (toIndex === 0) updatedJobExtend1 = sourceItem.jobExtend1;
                else if (toIndex === 1) updatedJobExtend2 = sourceItem.jobExtend2;
                else if (toIndex === 2) updatedJobExtend3 = sourceItem.jobExtend3;
              }

              return {
                ...item,
                persons: newPersons,
                jobExtend1: updatedJobExtend1,
                jobExtend2: updatedJobExtend2,
                jobExtend3: updatedJobExtend3,
              };
            }

            return item;
          }
          return item;
        });
      }
      // 处理第4-6个人选之间的跨行拖拽
      else if (fromIndex >= 4 && toIndex >= 4) {
        // 获取源候选人和目标候选人
        const fromCandidate = sourceItem.otherCandidateList?.find(c => c.index === fromIndex);
        const toCandidate = targetItem.otherCandidateList?.find(c => c.index === toIndex);

        return prevData.map(item => {
          if (item.id === fromItemId) {
            // 处理源行 - 移除源位置的候选人
            const newOtherCandidateList = [...(item.otherCandidateList || [])];
            const fromCandidateIndex = newOtherCandidateList.findIndex(c => c.index === fromIndex);

            if (fromCandidateIndex >= 0) {
              newOtherCandidateList.splice(fromCandidateIndex, 1);
            }

            return {
              ...item,
              otherCandidateList: newOtherCandidateList,
            };
          } else if (item.id === toItemId) {
            // 处理目标行
            const newOtherCandidateList = [...(item.otherCandidateList || [])];

            if (fromCandidate) {
              // 如果目标位置有候选人，将其移到方案级别的备选中
              if (toCandidate && item.chooseType == '1') {
                const alternativeDto = {
                  code: toCandidate.code || '',
                  name: toCandidate.name || '',
                  extendDto: {
                    ...toCandidate.extendDto,
                    proposePositionCode: item.positionCode || '',
                    proposePosition: item.positionName || '',
                  },
                };
                // 添加到方案级别的备选列表
                setAlternativeDtoList(prevList => [...prevList, alternativeDto]);

                // 移除目标位置的候选人
                const toCandidateIndex = newOtherCandidateList.findIndex(c => c.index === toIndex);
                if (toCandidateIndex >= 0) {
                  newOtherCandidateList.splice(toCandidateIndex, 1);
                }
              }

              // 创建新的候选人对象，更新岗位信息
              const newCandidate: OtherCandidateItem = {
                ...fromCandidate,
                index: toIndex,
                extendDto: {
                  ...fromCandidate.extendDto,
                  proposePositionCode: item.positionCode || '',
                  proposePosition: item.positionName || '',
                },
              };

              // 添加到目标位置
              newOtherCandidateList.push(newCandidate);
              newOtherCandidateList.sort((a, b) => a.index - b.index);
            }

            return {
              ...item,
              otherCandidateList: newOtherCandidateList,
            };
          }
          return item;
        });
      }
      // 处理1-3和4-6之间的跨行拖拽
      else if ((fromIndex < 3 && toIndex >= 4) || (fromIndex >= 4 && toIndex < 3)) {
        return prevData.map(item => {
          if (item.id === fromItemId) {
            // 处理源行
            if (fromIndex < 3) {
              // 从1-3位置移除人员
              const newPersons = [...item.persons];
              newPersons[fromIndex] = createEmptyPerson();
              return {
                ...item,
                persons: newPersons,
              };
            } else {
              // 从4-6位置移除候选人
              const newOtherCandidateList = [...(item.otherCandidateList || [])];
              const fromCandidateIndex = newOtherCandidateList.findIndex(c => c.index === fromIndex);

              if (fromCandidateIndex >= 0) {
                newOtherCandidateList.splice(fromCandidateIndex, 1);
              }

              return {
                ...item,
                otherCandidateList: newOtherCandidateList,
              };
            }
          } else if (item.id === toItemId) {
            // 处理目标行
            if (fromIndex < 3 && toIndex >= 4) {
              // 从1-3拖拽到4-6
              const fromPerson = sourceItem.persons[fromIndex];

              if (fromPerson && fromPerson.A0101) {
                const newOtherCandidateList = [...(item.otherCandidateList || [])];

                // 检查目标位置是否有候选人
                const toCandidateIndex = newOtherCandidateList.findIndex(c => c.index === toIndex);

                // 如果目标位置有候选人，将其移到方案级别的备选中
                if (toCandidateIndex >= 0 && item.chooseType == '1') {
                  const existingCandidate = newOtherCandidateList[toCandidateIndex];
                  const alternativeDto = {
                    code: existingCandidate.code || '',
                    name: existingCandidate.name || '',
                    extendDto: {
                      ...existingCandidate.extendDto,
                      proposePositionCode: item.positionCode || '',
                      proposePosition: item.positionName || '',
                    },
                  };
                  setAlternativeDtoList(prevList => [...prevList, alternativeDto]);
                  newOtherCandidateList.splice(toCandidateIndex, 1);
                }

                // 获取源人员的扩展数据
                const fromExtendData = fromIndex === 0 ? fromPerson.extendData1 : fromIndex === 1 ? fromPerson.extendData2 : fromPerson.extendData3;

                // 创建新的候选人对象
                const newCandidate: OtherCandidateItem = {
                  code: fromPerson.A00,
                  name: fromPerson.A0101,
                  index: toIndex,
                  extendDto: {
                    jobChangeCode: fromExtendData?.jobChangeCode || '',
                    jobChange: fromExtendData?.jobChange || '',
                    currentPositionCode: fromExtendData?.currentPositionCode || '',
                    currentPosition: fromExtendData?.currentPosition || fromPerson.XRZW || '',
                    proposePositionCode: item.positionCode || '',
                    proposePosition: item.positionName || '',
                    proposedRemovalPositionCode: fromExtendData?.proposedRemovalPositionCode || '',
                    proposedRemovalPosition: fromExtendData?.proposedRemovalPosition || '',
                    reason: fromExtendData?.reason || '',
                    introduction: fromExtendData?.introduction || '',
                    currPerson: fromPerson.A0101,
                    jobDescription: '',
                  },
                };

                newOtherCandidateList.push(newCandidate);
                newOtherCandidateList.sort((a, b) => a.index - b.index);

                return {
                  ...item,
                  otherCandidateList: newOtherCandidateList,
                };
              }
            } else if (fromIndex >= 4 && toIndex < 3) {
              // 从4-6拖拽到1-3
              const fromCandidate = sourceItem.otherCandidateList?.find(c => c.index === fromIndex);

              if (fromCandidate) {
                const newPersons = [...item.persons];
                const toPerson = newPersons[toIndex];

                // 如果目标位置有人员，将其移到方案级别的备选中
                if (toPerson && toPerson.A0101 && item.chooseType == '1') {
                  const toExtendData = toIndex === 0 ? toPerson.extendData1 : toIndex === 1 ? toPerson.extendData2 : toPerson.extendData3;

                  const alternativeDto = {
                    code: toPerson.A00 || '',
                    name: toPerson.A0101 || '',
                    extendDto: {
                      jobChangeCode: toExtendData?.jobChangeCode || '',
                      jobChange: toExtendData?.jobChange || '',
                      currentPositionCode: toExtendData?.currentPositionCode || '',
                      currentPosition: toExtendData?.currentPosition || toPerson.XRZW || '',
                      proposePositionCode: item.positionCode || '',
                      proposePosition: item.positionName || '',
                      proposedRemovalPositionCode: toExtendData?.proposedRemovalPositionCode || '',
                      proposedRemovalPosition: toExtendData?.proposedRemovalPosition || '',
                      reason: toExtendData?.reason || '',
                      introduction: toExtendData?.introduction || '',
                      currPerson: toPerson.A0101,
                      jobDescription: '',
                    },
                  };
                  setAlternativeDtoList(prevList => [...prevList, alternativeDto]);
                }

                // 创建新的人员对象
                const newPerson: PersonInfo = {
                  A00: fromCandidate.code,
                  A0101: fromCandidate.name,
                  XRZW: fromCandidate.extendDto?.currentPosition || '',
                };

                // 更新扩展数据并设置新的岗位信息
                const updatedExtendData = {
                  ...fromCandidate.extendDto,
                  proposePositionCode: item.positionCode || '',
                  proposePosition: item.positionName || '',
                };

                if (toIndex === 0) newPerson.extendData1 = updatedExtendData;
                else if (toIndex === 1) newPerson.extendData2 = updatedExtendData;
                else if (toIndex === 2) newPerson.extendData3 = updatedExtendData;

                newPersons[toIndex] = newPerson;

                return {
                  ...item,
                  persons: newPersons,
                };
              }
            }

            return item;
          }
          return item;
        });
      }

      return prevData;
    });
  };

  // 处理同行人员拖拽交换位置
  const handlePersonDragSwap = (itemId: string, fromIndex: number, toIndex: number) => {
    // 设置拖拽类型为同行拖拽
    dragTypeRef.current = 1;

    setData(prevData => {
      return prevData.map(item => {
        if (item.id === itemId) {
          // 处理前3个人选之间的拖拽（persons数组）
          if (fromIndex < 3 && toIndex < 3) {
            const newPersons = [...item.persons];
            const fromPerson = { ...newPersons[fromIndex] };
            const toPerson = { ...newPersons[toIndex] };

            // 获取扩展数据的辅助函数
            const getExtendDataByIndex = (person: PersonInfo, originalIndex: number) => {
              if (originalIndex === 0) return person.extendData1;
              if (originalIndex === 1) return person.extendData2;
              if (originalIndex === 2) return person.extendData3;
              return null;
            };

            // 保存原有的扩展数据
            const fromOriginalExtendData = getExtendDataByIndex(fromPerson, fromIndex);
            const toOriginalExtendData = getExtendDataByIndex(toPerson, toIndex);

            // 创建新的人员对象，重新映射扩展数据字段
            const newFromPerson: PersonInfo = {
              A00: fromPerson.A00,
              A0101: fromPerson.A0101,
              XRZW: fromPerson.XRZW,
            };

            const newToPerson: PersonInfo = {
              A00: toPerson.A00,
              A0101: toPerson.A0101,
              XRZW: toPerson.XRZW,
            };

            // 根据新位置重新设置extendData字段
            if (toIndex === 0) newFromPerson.extendData1 = fromOriginalExtendData;
            else if (toIndex === 1) newFromPerson.extendData2 = fromOriginalExtendData;
            else if (toIndex === 2) newFromPerson.extendData3 = fromOriginalExtendData;

            if (fromIndex === 0) newToPerson.extendData1 = toOriginalExtendData;
            else if (fromIndex === 1) newToPerson.extendData2 = toOriginalExtendData;
            else if (fromIndex === 2) newToPerson.extendData3 = toOriginalExtendData;

            // 交换人员位置
            newPersons[fromIndex] = newToPerson;
            newPersons[toIndex] = newFromPerson;

            // 处理jobExtend字段交换
            let updatedJobExtend1 = item.jobExtend1;
            let updatedJobExtend2 = item.jobExtend2;
            let updatedJobExtend3 = item.jobExtend3;

            if (item.chooseType == '2') {
              // 以人找岗模式：岗位说明跟随人员移动，需要交换jobExtend字段
              if (fromIndex === 0 && toIndex === 1) {
                updatedJobExtend1 = item.jobExtend2;
                updatedJobExtend2 = item.jobExtend1;
              } else if (fromIndex === 0 && toIndex === 2) {
                updatedJobExtend1 = item.jobExtend3;
                updatedJobExtend3 = item.jobExtend1;
              } else if (fromIndex === 1 && toIndex === 2) {
                updatedJobExtend2 = item.jobExtend3;
                updatedJobExtend3 = item.jobExtend2;
              } else if (fromIndex === 1 && toIndex === 0) {
                updatedJobExtend1 = item.jobExtend2;
                updatedJobExtend2 = item.jobExtend1;
              } else if (fromIndex === 2 && toIndex === 0) {
                updatedJobExtend1 = item.jobExtend3;
                updatedJobExtend3 = item.jobExtend1;
              } else if (fromIndex === 2 && toIndex === 1) {
                updatedJobExtend2 = item.jobExtend3;
                updatedJobExtend3 = item.jobExtend2;
              }
            }

            return {
              ...item,
              persons: newPersons,
              jobExtend1: updatedJobExtend1,
              jobExtend2: updatedJobExtend2,
              jobExtend3: updatedJobExtend3,
            };
          }
          // 处理第4-6个人选之间的拖拽（otherCandidateList数组）
          else if (fromIndex >= 4 && toIndex >= 4) {
            const newOtherCandidateList = [...(item.otherCandidateList || [])];

            // 找到对应的候选人
            const fromCandidateIndex = newOtherCandidateList.findIndex(c => c.index === fromIndex);
            const toCandidateIndex = newOtherCandidateList.findIndex(c => c.index === toIndex);

            if (fromCandidateIndex >= 0 && toCandidateIndex >= 0) {
              // 交换候选人的index值
              const fromCandidate = { ...newOtherCandidateList[fromCandidateIndex] };
              const toCandidate = { ...newOtherCandidateList[toCandidateIndex] };

              fromCandidate.index = toIndex;
              toCandidate.index = fromIndex;

              newOtherCandidateList[fromCandidateIndex] = toCandidate;
              newOtherCandidateList[toCandidateIndex] = fromCandidate;

              // 按index排序
              newOtherCandidateList.sort((a, b) => a.index - b.index);
            } else if (fromCandidateIndex >= 0) {
              // 只有源位置有候选人，移动到目标位置
              const fromCandidate = { ...newOtherCandidateList[fromCandidateIndex] };
              fromCandidate.index = toIndex;
              newOtherCandidateList[fromCandidateIndex] = fromCandidate;
              newOtherCandidateList.sort((a, b) => a.index - b.index);
            } else if (toCandidateIndex >= 0) {
              // 只有目标位置有候选人，移动到源位置
              const toCandidate = { ...newOtherCandidateList[toCandidateIndex] };
              toCandidate.index = fromIndex;
              newOtherCandidateList[toCandidateIndex] = toCandidate;
              newOtherCandidateList.sort((a, b) => a.index - b.index);
            }

            return {
              ...item,
              otherCandidateList: newOtherCandidateList,
            };
          }
          // 处理1-3和4-6之间的互相拖拽
          else if ((fromIndex < 3 && toIndex >= 4) || (fromIndex >= 4 && toIndex < 3)) {
            const newPersons = [...item.persons];
            const newOtherCandidateList = [...(item.otherCandidateList || [])];

            // 从1-3拖拽到4-6
            if (fromIndex < 3 && toIndex >= 4) {
              const fromPerson = newPersons[fromIndex];

              // 如果源位置有人员
              if (fromPerson && fromPerson.A0101) {
                // 获取源人员的扩展数据
                const fromExtendData = fromIndex === 0 ? fromPerson.extendData1 : fromIndex === 1 ? fromPerson.extendData2 : fromPerson.extendData3;

                // 检查目标位置是否有候选人
                const toCandidateIndex = newOtherCandidateList.findIndex(c => c.index === toIndex);

                // 如果目标位置有候选人，将其移到源位置
                if (toCandidateIndex >= 0) {
                  const toCandidate = newOtherCandidateList[toCandidateIndex];

                  // 创建新的人员对象
                  const newPerson: PersonInfo = {
                    A00: toCandidate.code,
                    A0101: toCandidate.name,
                    XRZW: toCandidate.extendDto?.currentPosition || '',
                  };

                  // 设置扩展数据
                  if (fromIndex === 0) newPerson.extendData1 = toCandidate.extendDto;
                  else if (fromIndex === 1) newPerson.extendData2 = toCandidate.extendDto;
                  else if (fromIndex === 2) newPerson.extendData3 = toCandidate.extendDto;

                  newPersons[fromIndex] = newPerson;

                  // 移除目标位置的候选人
                  newOtherCandidateList.splice(toCandidateIndex, 1);
                } else {
                  // 目标位置没有候选人，清空源位置
                  newPersons[fromIndex] = createEmptyPerson();
                }

                // 创建新的候选人对象
                const newCandidate: OtherCandidateItem = {
                  code: fromPerson.A00,
                  name: fromPerson.A0101,
                  index: toIndex,
                  extendDto: {
                    jobChangeCode: fromExtendData?.jobChangeCode || '',
                    jobChange: fromExtendData?.jobChange || '',
                    currentPositionCode: fromExtendData?.currentPositionCode || '',
                    currentPosition: fromExtendData?.currentPosition || fromPerson.XRZW || '',
                    proposePositionCode: fromExtendData?.proposePositionCode || '',
                    proposePosition: fromExtendData?.proposePosition || '',
                    proposedRemovalPositionCode: fromExtendData?.proposedRemovalPositionCode || '',
                    proposedRemovalPosition: fromExtendData?.proposedRemovalPosition || '',
                    reason: fromExtendData?.reason || '',
                    introduction: fromExtendData?.introduction || '',
                    currPerson: fromPerson.A0101,
                    jobDescription: '',
                  },
                };

                // 添加到候选人列表
                newOtherCandidateList.push(newCandidate);
                newOtherCandidateList.sort((a, b) => a.index - b.index);
              }
            }
            // 从4-6拖拽到1-3
            else if (fromIndex >= 4 && toIndex < 3) {
              const fromCandidateIndex = newOtherCandidateList.findIndex(c => c.index === fromIndex);

              // 如果源位置有候选人
              if (fromCandidateIndex >= 0) {
                const fromCandidate = newOtherCandidateList[fromCandidateIndex];
                const toPerson = newPersons[toIndex];

                // 如果目标位置有人员，将其移到源位置
                if (toPerson && toPerson.A0101) {
                  const toExtendData = toIndex === 0 ? toPerson.extendData1 : toIndex === 1 ? toPerson.extendData2 : toPerson.extendData3;

                  // 创建新的候选人对象
                  const newCandidate: OtherCandidateItem = {
                    code: toPerson.A00,
                    name: toPerson.A0101,
                    index: fromIndex,
                    extendDto: {
                      jobChangeCode: toExtendData?.jobChangeCode || '',
                      jobChange: toExtendData?.jobChange || '',
                      currentPositionCode: toExtendData?.currentPositionCode || '',
                      currentPosition: toExtendData?.currentPosition || toPerson.XRZW || '',
                      proposePositionCode: toExtendData?.proposePositionCode || '',
                      proposePosition: toExtendData?.proposePosition || '',
                      proposedRemovalPositionCode: toExtendData?.proposedRemovalPositionCode || '',
                      proposedRemovalPosition: toExtendData?.proposedRemovalPosition || '',
                      reason: toExtendData?.reason || '',
                      introduction: toExtendData?.introduction || '',
                      currPerson: toPerson.A0101,
                      jobDescription: '',
                    },
                  };

                  // 替换源位置的候选人
                  newOtherCandidateList[fromCandidateIndex] = newCandidate;
                } else {
                  // 目标位置没有人员，移除源位置的候选人
                  newOtherCandidateList.splice(fromCandidateIndex, 1);
                }

                // 创建新的人员对象
                const newPerson: PersonInfo = {
                  A00: fromCandidate.code,
                  A0101: fromCandidate.name,
                  XRZW: fromCandidate.extendDto?.currentPosition || '',
                };

                // 设置扩展数据
                if (toIndex === 0) newPerson.extendData1 = fromCandidate.extendDto;
                else if (toIndex === 1) newPerson.extendData2 = fromCandidate.extendDto;
                else if (toIndex === 2) newPerson.extendData3 = fromCandidate.extendDto;

                newPersons[toIndex] = newPerson;

                // 重新排序候选人列表
                newOtherCandidateList.sort((a, b) => a.index - b.index);
              }
            }

            return {
              ...item,
              persons: newPersons,
              otherCandidateList: newOtherCandidateList,
            };
          }
        }
        return item;
      });
    });
  };

  // 拖拽状态管理 - 使用ref避免页面刷新
  const dragStateRef = useRef({
    isDragging: false,
    dragItemId: null as string | null,
    dragIndex: null as number | null,
    startX: 0,
    startY: 0,
  });

  //点岗位名称的时候打开弹窗
  const openEditPositionModalVisible = () => {
    setEditPositionModalVisible(true);
  };

  // 打开人员下编辑模式
  const openExtendDataEidt = (id, index) => {
    setIsDataLoading(true);
    setData(prevData =>
      prevData.map(item => {
        if (item.id == id) {
          return {
            ...item,
            persons: item.persons.map((citem, cindex) => {
              if (cindex == index) {
                return {
                  ...citem,
                  isEdit: true,
                  // introduction: value,
                };
              }
              return citem;
            }),
          };
        }
        return item;
      })
    );
    setTimeout(() => {
      setIsDataLoading(false);
    }, 1000);
  };
  const changeExtendDataData = (id, index, value) => {
    // debugger;
    setData(prevData =>
      prevData.map(item => {
        if (item.id == id) {
          return {
            ...item,
            persons: item.persons.map((citem, cindex) => {
              if (cindex == index) {
                return {
                  ...citem,
                  isEdit: false,
                  [`extendData${index + 1}`]: {
                    ...citem[`extendData${index + 1}`],
                    introduction: value,
                  },
                };
              }
              return citem;
            }),
          };
        }
        return item;
      })
    );
  };

  // 提取表格组件，用于渲染表格头部和内容
  const TableComponent = ({ data, isReadOnly = false, tableWidth = '100%', onDataChange, isResultConfirm = false }: TableComponentProps) => {
    return (
      <div style={isReadOnly ? { maxHeight: '550px', overflowY: 'auto', marginBottom: 30, width: '100%' } : {}}>
        <div className={styles.tableHeadBox}>
          <table className={styles.sftable} style={{ width: tableWidth, borderBottom: 'none' }}>
            <colgroup>{renderCols()}</colgroup>
            <Head bgColor="#e4e6e9" tree={tableHead} nodeName={'name'} nodeKey={'key'} />
          </table>
        </div>
        <div className={styles.tableHeadBox} style={isReadOnly ? {} : {}}>
          <table className={styles.sftable} style={{ width: tableWidth, borderTop: 'none' }}>
            <colgroup>{renderCols()}</colgroup>
            <tbody>
              {!isEmpty(data) &&
                data.map((item: any, index: number) => {
                  //特殊类型
                  const specials = item.typeCode1 == '6' || item.typeCode1 == '5' || item.typeCode1 == '4';
                  // 检查是否展开第二行
                  const isExpanded = expandedRows.has(item.id);
                  console.log(expandedRows, item.id, item.chooseType, 'oiiiiiiiiiiiiiiiiiii');

                  // 检查是否为以岗找人模式
                  const isPositionToPersonMode = item.chooseType == '1';

                  return (
                    <React.Fragment key={item.id}>
                      {/* 第一行：原有的所有列 只读模式 禁止鼠标响应事件 */}
                      <tr className={styles.tableRow} style={{ pointerEvents: isReadOnly ? 'none' : 'auto' }}>
                        {/* 类型 */}
                        <td className={specials ? styles['dictCenter'] : ''} colSpan={specials ? 2 : 1} rowSpan={isExpanded ? 2 : 1}>
                          {/* @ts-ignore */}
                          <DictSelect
                            placeholder="请选择类型"
                            initValue={item.typeCode1}
                            codeType="SWZZBAPPIONT_PREPARE1"
                            backType="object"
                            onChange={value => handleTypeChange(value, item.id)}
                            showSearch={false}
                          />
                        </td>
                        {/* 干部退休  干部免值 晋升职级 的时候不显示 */}
                        <td style={{ display: specials ? 'none' : 'table-cell' }} rowSpan={isExpanded ? 2 : 1}>
                          {/* @ts-ignore */}
                          <DictSelect
                            placeholder="请选择类型"
                            initValue={item.typeCode2}
                            codeType="SWZZBAPPIONT_PREPARE2"
                            backType="object"
                            onChange={value => handleType2Change(value, item.id)}
                            showSearch={false}
                          />
                        </td>
                        {/* 岗位 */}
                        <td className={styles['person-select']} style={{ position: 'relative' }} onClick={() => changeShowPositionModal(item)} rowSpan={isExpanded ? 2 : 1}>
                          {!specials ? (
                            <div style={{ width: '100%', padding: 0, whiteSpace: 'pre-wrap', textAlign: 'center', cursor: 'pointer' }}>
                              <span
                                style={{ whiteSpace: 'pre-wrap' }}
                                onClick={async e => {
                                  if (item.chooseType == '1') {
                                    e.stopPropagation();
                                    await setCurrentRowData(item);
                                    openEditPositionModalVisible();
                                  }
                                }}
                              >
                                {item.positionName || ''}
                              </span>
                              {/* 岗位说明 */}
                              <br />
                              {item.chooseType == '1' && (
                                // <span></span>
                                <span style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all', display: 'inline-block', fontSize: 15, fontWeight: 100 }}>
                                  {item.jobExtend1?.jobDescription ? `（${item.jobExtend1?.jobDescription}）` : ''}
                                </span>
                              )}
                              {/* 下面的值 只有以人找岗的时候才会出现 三种可能：1.人选确认选定岗位的扩展数据(selectExtend) 2.后端返回的在extend1里面 3.刚刚新增选的 */}
                              {item.chooseType == '2' &&
                                (() => {
                                  const introduction = item?.extendData?.introduction || '';
                                  if (!introduction) return null;

                                  // 使用正则表达式匹配括号内的内容，但保留"（兼）"在原位置
                                  const tempText = introduction.replace(/（兼）/g, '@@JIANSIGN@@');
                                  const parts = tempText
                                    .split(/(\（[^）]*\）)/g)
                                    .filter(Boolean)
                                    .map(part => part.replace(/@@JIANSIGN@@/g, '（兼）'));

                                  return (
                                    <div style={{ textAlign: 'left', wordBreak: 'break-all', fontWeight: 100 }}>
                                      {parts.map((part, pIndex) => {
                                        // 检查是否是括号内容且不是"（兼）"
                                        const isSpecialBracket = part.startsWith('（') && part.endsWith('）') && !part.includes('（兼）');

                                        return (
                                          <p
                                            key={pIndex}
                                            style={{
                                              fontFamily: isSpecialBracket ? '方正仿宋' : '',
                                              fontSize: isSpecialBracket ? '16px' : '',
                                            }}
                                          >
                                            {part}
                                          </p>
                                        );
                                      })}
                                    </div>
                                  );
                                })()}
                            </div>
                          ) : (
                            <div style={{ width: '100%', height: '100%', padding: 0, whiteSpace: 'pre-wrap', textAlign: 'center', cursor: 'pointer' }}>
                              <Select
                                defaultValue={item.positionName}
                                onChange={e => handleSelectPositingChange(e, item.id)}
                                allowClear={true}
                                onClear={() => handleSelectPositingChange(undefined, item.id)}
                                style={{ width: '100%', fontSize: 'large', letterSpacing: '0.1em' }}
                              >
                                {(() => {
                                  if (item.typeCode1 == '6') {
                                    return (
                                      <Fragment>
                                        <Select.Option value="到龄退休">到龄退休</Select.Option>
                                        <Select.Option value="提前退休">提前退休</Select.Option>
                                      </Fragment>
                                    );
                                  }
                                  if (item.typeCode1 == '5') {
                                    return (
                                      <Fragment>
                                        <Select.Option value="干部免职">干部免职</Select.Option>
                                      </Fragment>
                                    );
                                  }
                                  if (item.typeCode1 == '4') {
                                    return (
                                      <Fragment>
                                        <Select.Option value="一级巡视员">一级巡视员</Select.Option>
                                        <Select.Option value="二级巡视员">二级巡视员</Select.Option>
                                      </Fragment>
                                    );
                                  }
                                })()}
                              </Select>
                            </div>
                          )}
                          {!specials && item.positionCode ? (
                            <DeleteOutlined
                              className={styles['hover-icon']}
                              style={{ position: 'absolute', top: 3, right: 3, cursor: 'pointer', color: '#ff4d4f' }}
                              onClick={e => {
                                e.stopPropagation();
                                handlePositionDelete(item.id);
                              }}
                            />
                          ) : null}
                        </td>

                        {/* 有关人员选择单元格 - 合并显示逻辑, 当 item.typeCode1 == '6' 干部退休  干部免值 晋升职级 的时候，单独一个特殊的td*/}
                        {specials ? (
                          <td
                            colSpan={3}
                            onClick={event => {
                              event.stopPropagation();
                              personSelectButtonRef?.current?.click?.();
                              setCurrentEditId(item.id);
                            }}
                          >
                            {!isEmpty(item?.CandidateSpecialList) &&
                              item?.CandidateSpecialList?.map?.((person, personIndex) => {
                                return (
                                  <Popover
                                    key={personIndex}
                                    content={
                                      <div>
                                        <a
                                          onClick={event => {
                                            event.stopPropagation();
                                            editCandidateSpecialListRow(personIndex, item.id);
                                          }}
                                        >
                                          编辑
                                        </a>
                                        <Divider type="vertical" />
                                        <a
                                          className="del"
                                          onClick={event => {
                                            delectCandidateSpecialListRow(personIndex, item.id);
                                            event.stopPropagation();
                                          }}
                                        >
                                          删除
                                        </a>
                                      </div>
                                    }
                                    title={person.candidateName}
                                  >
                                    <div style={{ textIndent: '0', display: 'flex', justifyContent: 'start', alignItems: 'center', paddingLeft: 20 }}>
                                      <span style={{ whiteSpace: 'nowrap', width: 60 }}>{person.candidateName}</span>
                                      {person.isEdit ? (
                                        <span style={{ fontFamily: 'fzfs', fontSize: 16, marginLeft: '10px' }}> {person.introduction}</span>
                                      ) : (
                                        <Input.TextArea
                                          onClick={event => event.stopPropagation()}
                                          style={{ flex: 1, marginLeft: '10px', fontSize: 16 }}
                                          defaultValue={person.introduction}
                                          onBlur={e => handleIntroductionChange(e.target.value, item.id, personIndex)}
                                        ></Input.TextArea>
                                      )}{' '}
                                    </div>
                                  </Popover>
                                );
                              })}
                          </td>
                        ) : (
                          (() => {
                            // 首先创建一个包含3个位置的完整单元格数组
                            const fullCells: React.ReactNode[] = [];

                            // 遍历所有3个位置
                            for (let i = 0; i < 3; i++) {
                              const person = item.persons[i];
                              const hasData = person && person.A0101;
                              fullCells.push(
                                <td
                                  key={`cell-${i}`}
                                  className={styles['person-select']}
                                  style={{
                                    position: 'relative',
                                    padding: '4px',
                                    verticalAlign: 'middle',
                                  }}
                                  // 拖拽放置区域
                                  onDragOver={e => {
                                    e.preventDefault();
                                    e.dataTransfer.dropEffect = 'move';
                                    // 添加拖拽悬停样式
                                    e.currentTarget.classList.add(styles['drag-over']);
                                  }}
                                  onDragLeave={e => {
                                    // 移除拖拽悬停样式
                                    e.currentTarget.classList.remove(styles['drag-over']);
                                  }}
                                  onDrop={e => {
                                    e.preventDefault();
                                    e.currentTarget.classList.remove(styles['drag-over']);
                                    try {
                                      const dragData = JSON.parse(e.dataTransfer.getData('text/plain'));

                                      if (dragData.type === 'alternative') {
                                        // 处理从备选列拖拽过来的数据
                                        handleAlternativeDrop(dragData, item.id, i);
                                      } else {
                                        // 处理人员拖拽
                                        // 不能拖拽到相同位置
                                        if (dragData.itemId === item.id && dragData.fromIndex === i) {
                                          return;
                                        }

                                        if (dragData.itemId === item.id) {
                                          // 同行内交换
                                          handlePersonDragSwap(item.id, dragData.fromIndex, i);
                                        } else {
                                          // 跨行移动
                                          handleCrossRowDragSwap(dragData.itemId, dragData.fromIndex, item.id, i);
                                        }
                                      }
                                    } catch (error) {
                                      console.error('拖拽数据解析失败:', error);
                                    }
                                  }}
                                >
                                  {hasData ? (
                                    <>
                                      <div
                                        draggable="true"
                                        data-drag-item-id={item.id}
                                        data-drag-index={i}
                                        style={{
                                          padding: 0,
                                          whiteSpace: 'pre-wrap',
                                          textAlign: 'center',
                                          userSelect: 'none',
                                        }}
                                        onDragStart={e => {
                                          // 阻止事件冒泡
                                          e.stopPropagation();

                                          // 设置拖拽数据
                                          e.dataTransfer.setData(
                                            'text/plain',
                                            JSON.stringify({
                                              itemId: item.id,
                                              fromIndex: i,
                                              personData: person,
                                            })
                                          );
                                          e.dataTransfer.effectAllowed = 'move';

                                          // 设置拖拽样式
                                          e.currentTarget.style.opacity = '0.5';

                                          // 使用ref设置状态，避免页面刷新
                                          dragStateRef.current = {
                                            isDragging: true,
                                            dragItemId: item.id,
                                            dragIndex: i,
                                            startX: 0,
                                            startY: 0,
                                          };
                                        }}
                                        onDragEnd={e => {
                                          // 恢复样式
                                          e.currentTarget.style.opacity = '1';
                                          // 使用ref清理拖拽状态，避免页面刷新
                                          dragStateRef.current = {
                                            isDragging: false,
                                            dragItemId: null,
                                            dragIndex: null,
                                            startX: 0,
                                            startY: 0,
                                          };
                                        }}
                                        onClick={e => {
                                          // 延迟检查拖拽状态，因为 拖拽开始时状态可能还没更新
                                          setTimeout(() => {
                                            if (!dragStateRef.current.isDragging) {
                                              const chooseType = item?.chooseType || '1';
                                              if (hasData) {
                                                //这是点击弹出人员选择框
                                                if (chooseType == '1') {
                                                  openConfirmInfoModal(item.id, i, chooseType);
                                                }
                                                // 2025/6/19  刘浩然  点击后弹出人员确认框
                                                if (chooseType == '2') {
                                                  showPersonSelectModal(item.id, i, chooseType);
                                                }
                                              } else {
                                                showPersonSelectModal(item.id, i, chooseType);
                                              }
                                            }
                                          }, 10);
                                        }}
                                      >
                                        <React.Fragment>
                                          {/* 人选确认模式下显示勾选框 */}
                                          {isResultConfirm && onDataChange && (
                                            <Checkbox
                                              checked={item.isChecked || false}
                                              onChange={() => handleCheckPersonInResultConfirm(item.id, item.code, onDataChange)}
                                              style={{ marginRight: 8 }}
                                            />
                                          )}
                                          {person.A0101}
                                          {/* 岗位说明 */}
                                          {item.chooseType == '2' &&
                                            (() => {
                                              const jobExtend = i === 0 ? item.jobExtend1 : i === 1 ? item.jobExtend2 : item.jobExtend3;
                                              return <p style={{ textIndent: 'unset' }}>{jobExtend?.jobDescription ? `（${jobExtend?.jobDescription}）` : ''}</p>;
                                            })()}
                                          {item.chooseType != '2' &&
                                            (() => {
                                              const introduction = person[`extendData${i + 1}`]?.introduction || '';
                                              if (!introduction) return null;

                                              // 使用正则表达式匹配括号内的内容，但保留"（兼）"在原位置
                                              const tempText = introduction.replace(/（兼）/g, '@@JIANSIGN@@');
                                              const parts = tempText
                                                .split(/(\（[^）]*\）)/g)
                                                .filter(Boolean)
                                                .map(part => part.replace(/@@JIANSIGN@@/g, '（兼）'));

                                              return (
                                                <div style={{ textAlign: 'left', wordBreak: 'break-all' }}>
                                                  {!person.isEdit ? (
                                                    parts.map((part, pIndex) => {
                                                      // 检查是否是括号内容且不是"（兼）"
                                                      const isSpecialBracket = part.startsWith('（') && part.endsWith('）') && !part.includes('（兼）');

                                                      return (
                                                        <p
                                                          key={pIndex}
                                                          style={{
                                                            fontFamily: isSpecialBracket ? 'fzfs' : '',
                                                            fontSize: isSpecialBracket ? '16px' : '',
                                                          }}
                                                          onClick={e => {
                                                            e.stopPropagation();
                                                            openExtendDataEidt(item.id, i);
                                                          }}
                                                        >
                                                          {part}
                                                        </p>
                                                      );
                                                    })
                                                  ) : (
                                                    <TextArea
                                                      autoSize={true}
                                                      onClick={e => {
                                                        e.stopPropagation();
                                                      }}
                                                      defaultValue={tempText}
                                                      onBlur={e => changeExtendDataData(item.id, i, e.target.value)}
                                                    />
                                                  )}
                                                </div>
                                              );
                                            })()}
                                        </React.Fragment>
                                      </div>
                                      <DeleteOutlined
                                        className={styles['hover-icon']}
                                        style={{ position: 'absolute', top: 5, right: 5, cursor: 'pointer', color: '#ff4d4f' }}
                                        onClick={e => {
                                          e.stopPropagation();
                                          handlePersonDelete(item, i);
                                        }}
                                      />
                                    </>
                                  ) : (
                                    <>
                                      <div
                                        style={{ height: '100%', width: '100%' }}
                                        onClick={() => {
                                          const chooseType = item?.chooseType || '1';
                                          showPersonSelectModal(item.id, i, chooseType);
                                        }}
                                        onMouseEnter={e => {
                                          e.currentTarget.style.borderColor = '#1890ff';
                                          e.currentTarget.style.color = '#1890ff';
                                        }}
                                        onMouseLeave={e => {
                                          e.currentTarget.style.borderColor = '#d9d9d9';
                                          e.currentTarget.style.color = '#bfbfbf';
                                        }}
                                      ></div>
                                    </>
                                  )}
                                </td>
                              );
                            }

                            return fullCells;
                          })()
                        )}

                        {/* 备注 */}
                        <td style={{ position: 'relative' }} rowSpan={isExpanded ? 2 : 1}>
                          <div style={{ height: '100%' }} onClick={() => handleOpenRemarkModal(item)}>
                            {item.remarksDTO &&
                              (() => {
                                // 如果是对象，直接使用
                                const remarkData: any = item.remarksDTO ?? {};
                                return (
                                  <div>
                                    <div style={{ fontSize: 14 }} onClick={e => e.stopPropagation()}>
                                      <a href={`/api${remarkData.rmFile}`}>{remarkData.rmFile ? remarkData.rmFile.split('\\').pop() : ''}</a>
                                    </div>
                                    <div style={{ fontSize: 14 }} onClick={e => e.stopPropagation()}>
                                      <a href={`/api${remarkData.fjFile}`}>{remarkData.fjFile ? remarkData.fjFile.split('\\').pop() : ''}</a>
                                    </div>
                                    <div>{remarkData.content}</div>
                                  </div>
                                );
                              })()}
                          </div>
                          {isReadOnly ? (
                            <React.Fragment />
                          ) : (
                            <div className={styles.action}>
                              <Button type="text" icon={<ArrowUpOutlined />} title="上移" onClick={() => handleMoveUp(item.id)} disabled={index === 0} />
                              <Button type="text" icon={<ArrowDownOutlined />} title="下移" onClick={() => handleMoveDown(item.id)} disabled={index === data.length - 1} />
                              <Button type="text" danger icon={<DeleteOutlined />} title="删除" onClick={() => handleDelete(item.id)} disabled={data.length <= 1} />
                              {/* {buttons?.add !== false && <Button type="text" icon={<PlusOutlined />} title="添加" onClick={handleAdd} />} */}
                              {/* 干部一处 按钮旁边取消  上面是没取消 */}
                              {<Button type="text" icon={<PlusOutlined />} title="添加" onClick={() => handleAdd(item, index)} />}
                              {/* 更多人选切换按钮 - 只在以岗找人模式下显示 */}
                              {!specials && isPositionToPersonMode && (
                                <div style={{ fontSize: '12px', cursor: 'pointer', textAlign: 'center' }} onClick={() => toggleRowExpansion(item.id)}>
                                  {isExpanded ? '收起' : '更多'}
                                </div>
                              )}
                            </div>
                          )}
                        </td>
                      </tr>

                      {/* 第二行：显示第4-6个人选（只在以岗找人模式且展开时显示） */}
                      {isPositionToPersonMode && isExpanded && (
                        <tr className={styles.tableRow}>
                          {/* 前3列已经被第一行合并，这里显示第4-6个人员位置 */}
                          {[4, 5, 6].map(personIndex => {
                            // 查找对应位置的候选人
                            const candidate = item.otherCandidateList?.find(c => c.index === personIndex);
                            const hasData = candidate && candidate.name;

                            return (
                              <td
                                key={personIndex}
                                className={styles['person-select']}
                                style={{
                                  position: 'relative',
                                  padding: '4px',
                                  verticalAlign: 'middle',
                                }}
                                // 拖拽放置区域
                                onDragOver={e => {
                                  e.preventDefault();
                                  e.dataTransfer.dropEffect = 'move';
                                  // 添加拖拽悬停样式
                                  e.currentTarget.classList.add(styles['drag-over']);
                                }}
                                onDragLeave={e => {
                                  // 移除拖拽悬停样式
                                  e.currentTarget.classList.remove(styles['drag-over']);
                                }}
                                onDrop={e => {
                                  e.preventDefault();
                                  e.currentTarget.classList.remove(styles['drag-over']);
                                  try {
                                    const dragData = JSON.parse(e.dataTransfer.getData('text/plain'));

                                    if (dragData.type === 'alternative') {
                                      // 处理从备选列拖拽过来的数据
                                      handleAlternativeDrop(dragData, item.id, personIndex);
                                    } else {
                                      // 处理人员拖拽
                                      // 不能拖拽到相同位置
                                      if (dragData.itemId === item.id && dragData.fromIndex === personIndex) {
                                        return;
                                      }

                                      if (dragData.itemId === item.id) {
                                        // 同行内交换
                                        handlePersonDragSwap(item.id, dragData.fromIndex, personIndex);
                                      } else {
                                        // 跨行移动
                                        handleCrossRowDragSwap(dragData.itemId, dragData.fromIndex, item.id, personIndex);
                                      }
                                    }
                                  } catch (error) {
                                    console.error('拖拽数据解析失败:', error);
                                  }
                                }}
                              >
                                {hasData ? (
                                  <>
                                    <div
                                      draggable="true"
                                      data-drag-item-id={item.id}
                                      data-drag-index={personIndex}
                                      style={{
                                        padding: 0,
                                        whiteSpace: 'pre-wrap',
                                        textAlign: 'center',
                                        userSelect: 'none',
                                        position: 'relative',
                                        cursor: 'pointer',
                                      }}
                                      className="person-item-hover"
                                      onDragStart={e => {
                                        // 阻止事件冒泡
                                        e.stopPropagation();

                                        // 设置拖拽数据
                                        e.dataTransfer.setData(
                                          'text/plain',
                                          JSON.stringify({
                                            itemId: item.id,
                                            fromIndex: personIndex,
                                          })
                                        );

                                        // 设置拖拽样式
                                        e.currentTarget.style.opacity = '0.5';

                                        // 使用ref设置状态，避免页面刷新
                                        dragStateRef.current = {
                                          isDragging: true,
                                          dragItemId: item.id,
                                          dragIndex: personIndex,
                                          startX: 0,
                                          startY: 0,
                                        };
                                      }}
                                      onDragEnd={e => {
                                        // 恢复样式
                                        e.currentTarget.style.opacity = '1';
                                        // 使用ref清理拖拽状态，避免页面刷新
                                        dragStateRef.current = {
                                          isDragging: false,
                                          dragItemId: null,
                                          dragIndex: null,
                                          startX: 0,
                                          startY: 0,
                                        };
                                      }}
                                      onClick={e => {
                                        // 延迟检查拖拽状态，因为 拖拽开始时状态可能还没更新
                                        setTimeout(() => {
                                          if (!dragStateRef.current.isDragging) {
                                            showPersonSelectModal(item.id, personIndex, item.chooseType);
                                          }
                                        }, 100);
                                      }}
                                    >
                                      <React.Fragment>
                                        {/* 人选确认模式下显示勾选框 */}
                                        {isResultConfirm && onDataChange && (
                                          <Checkbox
                                            checked={candidate.isChecked || false}
                                            onChange={() => handleCheckPersonInResultConfirm(item.id, candidate.code, onDataChange)}
                                            style={{ marginRight: 8 }}
                                          />
                                        )}
                                        {candidate.name}
                                        {/* 简介显示 */}
                                        {candidate.extendDto?.introduction && (
                                          <div style={{ textAlign: 'left', wordBreak: 'break-all' }}>
                                            <p>{candidate.extendDto.introduction}</p>
                                          </div>
                                        )}
                                      </React.Fragment>
                                    </div>
                                    {/* 删除按钮 */}
                                    <DeleteOutlined
                                      className={styles['hover-icon']}
                                      style={{
                                        position: 'absolute',
                                        top: '5px',
                                        right: '5px',
                                        color: '#ff4d4f',
                                        cursor: 'pointer',
                                        fontSize: '12px',
                                      }}
                                      onClick={e => {
                                        e.stopPropagation();
                                        const candidateIndex = item.otherCandidateList?.findIndex(c => c.index === personIndex) || 0;
                                        handleDeleteOtherCandidate(item.id, candidateIndex);
                                      }}
                                    />
                                  </>
                                ) : (
                                  <div
                                    style={{
                                      height: '40px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      cursor: isReadOnly ? 'default' : 'pointer',
                                      color: '#999',
                                      fontSize: '12px',
                                      pointerEvents: isReadOnly ? 'none' : 'auto',
                                    }}
                                    onClick={() => !isReadOnly && showPersonSelectModal(item.id, personIndex, item.chooseType)}
                                  ></div>
                                )}
                              </td>
                            );
                          })}
                        </tr>
                      )}
                    </React.Fragment>
                  );
                })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // 渲染版本详情表格
  const renderVersionTable = (versionData: VersionHistoryItem) => {
    // 将版本记录转换为适合展示的格式
    const tableItems = versionData.records.map((item, index) => ({
      id: item.code || `temp_${index}`,
      type1: item.type1 || '',
      typeCode1: item.typeCode1 || '',
      type2: item.type2 || '',
      typeCode2: item.typeCode2 || '',
      chooseType: item.chooseType,
      positionName: item.positionName || '',
      persons: [{ A0101: item.candidateName1 || '' }, { A0101: item.candidateName2 || '' }, { A0101: item.candidateName3 || '' }],
      remarksDTO: item.remarksDTO || { rmFile: '', fjFile: '', content: '' },
      CandidateSpecialList: item.CandidateSpecialList || [],
      otherCandidateList: item.otherCandidateList || [], // 添加第4-6个有关人选列表
    }));
    console.log(tableItems, 'tableItems');
    return (
      <div className={styles.versionSection} key={versionData.key}>
        <div style={{ marginBottom: 16, padding: '12px 16px', background: '#f5f5f5', borderRadius: 4, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <span style={{ fontSize: 16, fontWeight: 'bold' }}>版本号: {versionData.version}</span>
            <span>创建时间: {moment(versionData.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
            <span>记录数: {versionData.records.length}条</span>
          </Space>
          <Button type="primary" onClick={() => applyVersion(versionData)}>
            应用此版本
          </Button>
        </div>

        {/* 使用通用表格组件渲染版本详情 */}
        <TableComponent data={tableItems} isReadOnly={true} />
      </div>
    );
  };

  // 将"共享方案"修改为"提交"功能
  const [submitModalVisible, setSubmitModalVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [submitUserList, setSubmitUserList] = useState<any[]>([]);
  const [selectedUserKeys, setSelectedUserKeys] = useState<string[]>([]);
  //
  const [submitProposalType, setSubmitProposalType] = useState<number>();

  // 列表操作和确认结果是否可以点击
  const [allowClick, setAllowClick] = useState(false);

  // 加载可提交的用户列表
  const loadSubmitUsers = async type => {
    // type 1提交合稿方案 2提交审核方案
    try {
      setSubmitLoading(true);
      const res = await request(`/api/swzzbappoint/prepareCadres/findSubmitUser?type=${type}`);

      if (res && res.code === 0 && Array.isArray(res.data)) {
        setSubmitUserList(res.data);
      } else {
        message.error(res?.message || '获取用户列表失败');
        setSubmitUserList([]);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
      setSubmitUserList([]);
    } finally {
      setSubmitLoading(false);
    }
  };

  // 显示提交弹窗
  const showSubmitModal = async type => {
    setSubmitProposalType(type);
    const res = await saveScheme(2);
    if (!res) return;
    // if (!schemeId) {
    //   message.warning('请先保存方案再提交');
    //   return;
    // }
    setSelectedUserKeys([]);
    setSubmitModalVisible(true);
    loadSubmitUsers(type);
  };

  // 处理用户选择变化
  const handleUserSelectChange = (selectedRowKeys: string[]) => {
    setSelectedUserKeys(selectedRowKeys);
  };

  // 提交方案
  const handleSubmit = async () => {
    if (!schemeId) {
      message.warning('请先保存方案');
      return;
    }

    if (selectedUserKeys.length === 0) {
      message.warning('请选择至少一个接收人');
      return;
    }

    try {
      setSubmitLoading(true);

      const res = await request('/api/swzzbappoint/prepareCadres/submitPrepareCadres', {
        method: 'POST',
        body: {
          data: {
            codes: selectedUserKeys,
            planCode: schemeId,
            submitType: submitProposalType,
          },
        },
      });

      if (res && res.code === 0) {
        message.success('提交成功');
        setSubmitModalVisible(false);
      } else {
        message.error(res?.message || '提交失败');
      }
    } catch (error) {
      console.error('提交方案失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setSubmitLoading(false);
    }
  };

  // 共享方案
  const handleSharePlan = async () => {
    if (!schemeId) {
      message.warning('请先保存方案再共享');
      return;
    }

    try {
      setLoading(true);
      const res = await sharePlan({ code: schemeId, shareStatus: 1 });
      if (res.code === 0) {
        message.success('共享成功');
      } else {
        message.error(res.message || '共享失败');
      }
    } catch (error) {
      console.error('共享方案失败:', error);
      message.error('共享失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 导入方案 - 显示上传弹窗
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importFileList, setImportFileList] = useState<any[]>([]);
  const [importLoading, setImportLoading] = useState(false);

  // 显示导入弹窗
  const showImportModal = () => {
    setImportFileList([]);
    setImportModalVisible(true);
  };

  // 处理文件上传前的检查
  const beforeUpload = file => {
    // 检查文件类型
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
      message.error('只能上传Excel文件!');
      return Upload.LIST_IGNORE;
    }

    // 检查文件大小
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB!');
      return Upload.LIST_IGNORE;
    }

    // 添加到文件列表
    setImportFileList([file]);
    return false; // 阻止自动上传
  };

  // 提交导入
  const handleImportSubmit = async () => {
    if (importFileList.length === 0) {
      message.warning('请选择要导入的文件');
      return;
    }

    try {
      setImportLoading(true);

      // 创建表单数据
      const formData = new FormData();
      formData.append('file', importFileList[0]);
      if (schemeId) {
        formData.append('planCode', schemeId);
      }

      // 调用导入API
      const res = await importPlan({ data: formData });

      if (res.code === 0) {
        message.success('导入成功');
        setImportModalVisible(false);

        // 如果是新建，保存后设置ID
        if (!schemeId && res.data && res.data.planCode) {
          setSchemeId(res.data.planCode);
          window.history.replaceState(null, '', `?code=${res.data.planCode}`);
        }

        // 重新加载数据
        if (schemeId || (res.data && res.data.planCode)) {
          loadScheme(schemeId || res.data.planCode);
        }
      } else {
        message.error(res.message || '导入失败');
      }
    } catch (error) {
      console.error('导入方案失败:', error);
      message.error('导入失败，请重试');
    } finally {
      setImportLoading(false);
    }
  };

  // 处理导出方案
  const handleExportPlan = async () => {
    if (!schemeId) {
      message.warning('请先保存方案再导出');
      return;
    }

    try {
      setLoading(true);
      const res = await exportPlan({ planCode: schemeId, type: 1 });

      // 使用fileDownload函数下载文件
      fileDownload(`/api${res.data.url}`);

      message.success('导出成功');
    } catch (error) {
      console.error('导出方案失败:', error);
      message.error('导出失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const isShowConfirmInfoModalChange = () => {
    const rowData: any = data.find(item => item.id === currentEditId);
    if (!rowData) {
      return true;
    }
    if (rowData.typeCode1 == '6' || rowData.typeCode1 == '5' || rowData.typeCode1 == '4') {
      return false;
    }
    return true;
  };

  // 判断是否需要多选模式
  const isMultipleSelectMode = () => {
    const rowData: any = data.find(item => item.id === currentEditId);
    if (!rowData) {
      return false;
    }
    // 只有晋升职级、干部退休需要多选（干部免职不需要多选）
    return rowData.typeCode1 == '4' || rowData.typeCode1 == '6';
  };
  // 处理文件上传前的检查
  const beforeUpload1 = file => {
    // 检查文件大小
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('文件大小不能超过2MB!');
      return Upload.LIST_IGNORE;
    }

    setImportLoading1(true);
    return true;
  };
  // 导入文件
  const importFile = async file => {
    if (file.url) {
      setFileUrl(file.url);
      try {
        const importRes = await request(`/api/swzzbappoint/examinationMaterials/uploadArchiveMaterials?id=${querys?.id}&url=${file.url}`, {
          method: 'GET',
        });

        if (importRes && importRes.code === 0) {
          message.success('导入成功');
          // 导入成功后页面显示只导入的内容，不再显示原来的页面
          setHtmlContent(importRes.data?.archiveMaterialsFile || '');
          setFileUrl(importRes.data?.fileUrl || '');
          sessionStorage.setItem('archiveMaterialsFile', importRes.data?.archiveMaterialsFile || ''); // 保存html内容到sessionStorage
          // 刷新综合任免卷 页面
          localStorage.setItem('archiveMaterialsPageKey', `${+new Date()}`);
          // 更新路由上的fileUrl参数
          const query = { ..._history.location.query, fileUrl: importRes.data?.fileUrl || '' };
          _history.replace({
            pathname: _history.location.pathname,
            query,
          });
        } else {
          message.error(importRes.message || '导入失败');
        }
      } catch (error) {
        console.error('导入失败:', error);
        message.error('导入失败，请重试');
      } finally {
        setImportLoading1(false);
      }
    }
  };
  // 处理文件上传变更
  const handleUploadChange = info => {
    if (info.file.status === 'done') {
      if (info.file.response && info.file.response.code === 0) {
        const file = info.file.response.data[0];

        // 调用导入接口
        importFile(file);
      } else {
        message.error(info.file.response?.message || '文件上传失败');
        setImportLoading1(false);
      }
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
      setImportLoading1(false);
    }
  };
  // 导出文件
  const doDownload = () => {
    if (fileUrl) {
      fileDownload(`/api${fileUrl}`);
    }
  };
  const showLog = () => {
    getLogList(1, 10);
    setLogVisible(true);
  };

  // 以岗找人 编辑岗位
  const onConfirmEditPositionModal = values => {
    console.log(values);
    setData(prevData =>
      prevData.map(item => {
        if (item.id == currentRowData?.id) {
          return {
            ...item,
            jobExtend1: {
              ...item.jobExtend1,
              jobDescription: values?.jobDescription,
              fileUrl: values?.fileUrl,
            },
            positionName: values?.positionName,
          };
        }
        return item;
      })
    );
    setEditPositionModalVisible(false);
  };

  // 传递给人员确认框的拟任职务
  const returnCurrentRowData = () => {
    const currentRow = data.filter(item => item.id == currentEditId)[0];
    if (!currentRow) return null;

    // 将平级字段转换为 PersonModalSelect 期望的 position 对象结构
    return {
      ...currentRow,
      position: {
        value: currentRow.positionCode || '',
        label: currentRow.positionName || '',
        chooseType: currentRow.chooseType || '',
        jobExtend1: currentRow.jobExtend1,
        jobExtend2: currentRow.jobExtend2,
        jobExtend3: currentRow.jobExtend3,
        personConfirmValues: currentRow.personConfirmValues,
      },
    };
  };
  // 关闭读取共享方案弹窗后要刷新当前页面
  const gxfaBack = () => {
    setFaVisible(false);
    loadScheme(schemeId);
  };
  return (
    <div onClick={() => setAlternativePanelVisible(false)}>
      <div style={{ fontWeight: 'bold' }}>{planName}</div>
      {querys?.comprehensive !== 'true' && (
        <Space>
          {/* 待审核方案(type=4)时只显示导出按钮 */}
          {_history.location.query?.type == '4' ? (
            <>
              {buttons.exportPlan && (
                <Button type="primary" onClick={handleExportPlan}>
                  导出方案
                </Button>
              )}
              {buttons.exportIpadData && (
                <Button type="primary" onClick={() => {}}>
                  导出平板数据包
                </Button>
              )}
            </>
          ) : (
            <>
              {buttons.save && (
                <Button type="primary" onClick={() => saveScheme()} loading={loading}>
                  保存
                </Button>
              )}
              {/* {buttons.preview && <Button type="primary">预览</Button>} */}
              {buttons.saveAs && (
                <Button type="primary" onClick={saveAsScheme}>
                  另存为
                </Button>
              )}
              {buttons.version && (
                <Button type="primary" onClick={showVersionModal}>
                  版本管理
                </Button>
              )}
              {buttons.sharedPlans && (
                <Button type="primary" onClick={showSharedPlansModal}>
                  读取提交方案
                </Button>
              )}
              {buttons.resultConfirm && (
                <Button type="primary" disabled={allowClick} onClick={handleResultConfirm}>
                  人选确认
                </Button>
              )}
              {buttons.share && (
                <Button type="primary" onClick={() => showSubmitModal(1)}>
                  提交合稿方案
                </Button>
              )}
              {/* 一处 */}
              {buttons.share && (
                <Button type="primary" onClick={() => showSubmitModal(2)}>
                  提交审核方案
                </Button>
              )}
              {/* {buttons.importPlan && (
              <Button type="primary" onClick={showImportModal}>
                导入方案
              </Button>
            )} */}
              {buttons.exportPlan && (
                <Button type="primary" onClick={handleExportPlan}>
                  导出方案
                </Button>
              )}
              {buttons.exportIpadData && (
                <Button type="primary" onClick={() => {}}>
                  导出平板数据包
                </Button>
              )}
              <Button type="primary" onClick={showLog}>
                操作日志
              </Button>
              {buttons.sort && (
                <Button type="primary" onClick={() => {}}>
                  排序
                </Button>
              )}
            </>
          )}
        </Space>
      )}
      {querys?.comprehensive === 'true' && (
        <Space>
          <Upload
            headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
            action="/api/base/upload"
            beforeUpload={beforeUpload1}
            onChange={handleUploadChange}
            showUploadList={false}
            accept=".docx,.doc"
          >
            <Button type="primary" loading={importLoading1}>
              导入文件
            </Button>
          </Upload>
          <Button type="primary" onClick={fileUrl ? doDownload : buttons.exportPlan ? handleExportPlan : () => {}}>
            导出文件
          </Button>
        </Space>
      )}
      {htmlContent ? (
        <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
      ) : (
        <div className={styles.dyContainer}>
          <div className={styles.dyLeft}>
            <FileAside></FileAside>
          </div>
          <div className={styles.dyRight}>
            <div style={{ width: WIDTH, margin: '0 auto' }}>
              <div
                style={{
                  position: 'absolute',
                  right: 0,
                  padding: '12px',
                  borderRadius: '2px',
                  textAlign: 'center',
                  color: 'white',
                  backgroundColor: '#40a9ff',
                }}
                onClick={e => {
                  e.stopPropagation();
                  setAlternativePanelVisible(alternativePanelVisible ? false : true);
                }}
              >
                {/* 备选人员按钮 */}
                <span style={{ fontWeight: 500 }}>
                  备<br />
                  选<br />
                  人<br />
                  员<br />({alternativeDtoList.length})
                </span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                {!buttons.isHiddenRightTopCard && <Note time={reportingTime} setTime={setReportingTime} content={reportContent} setContent={setReportContent} />}
                {buttons.isHiddenRightTopCard && <div style={{ height: '111px', width: '100%' }}></div>}
              </div>
              <div className={styles.tableTitle}>
                提交酝酿的干部
                <div className={styles.desc}>（涉及{totalPersons}人）</div>
              </div>
              <div className={styles.tableInfo}>
                <span>
                  制表时间：
                  <div style={{ display: 'inline-block' }}>
                    <TimePicker regNowDate={false} value={currentTime} onChange={value => setCurrentTime(value)} format="YYYY.MM.DD"></TimePicker>
                  </div>
                </span>
                <span>
                  制表单位：
                  <div style={{ display: 'inline-block' }}>
                    <Input value={unitName} onChange={handleUnitNameChange}></Input>
                  </div>
                </span>
              </div>

              {/* 使用通用表格组件渲染主界面表格 */}
              <TableComponent data={data} isReadOnly={allowClick} />
            </div>
          </div>
        </div>
      )}

      {/* 岗位选择弹窗 */}
      <PositionSelectModal
        visible={positionModalVisible}
        onCancel={() => setPositionModalVisible(false)}
        onSelect={handlePositionSelect}
        rowData={currentRowData as any} // 使用类型断言解决类型错误
      />

      {/* 人员选择组件，使用按钮引用 */}
      <div style={{ display: 'none' }}>
        <PersonModalSelect
          onChange={handlePersonSelectConfirm}
          planCode={schemeId}
          title={'选择人员'}
          resetSelected={true} // 设置每次打开都重置选择
          filterPersonList={filterPersonList} //过滤掉已经选择过得人员
          isShowConfirmInfoModal={isShowConfirmInfoModalChange()} //类型为干部退休 干部免职 晋升职级不要人员确认弹窗
          multipleSelect={isMultipleSelectMode()} // 晋升职级和干部退休启用多选
          maxCount={10} // 设置最大选择数量
          currentRowData={returnCurrentRowData()}
          AppointmentData={{
            prepareCadresCode: currentRowData ? currentRowData?.code : undefined,
            planCode: schemeId,
          }}
          //备选人员不允许保存任免表
          saveA01Info={currentEditId == 'alternative-add' ? false : true}
        >
          <Button ref={personSelectButtonRef} type="link">
            隐藏按钮
          </Button>
        </PersonModalSelect>
      </div>

      {/* 版本管理弹窗 */}
      <Modal title="版本管理" maskClosable={false} visible={versionModalVisible} onCancel={() => setVersionModalVisible(false)} width={1300} footer={null}>
        {versionLoading ? (
          <div style={{ textAlign: 'center', padding: '30px 0' }}>
            <div style={{ marginBottom: 8 }}>加载版本历史中...</div>
            <div>
              <Button type="primary" loading={true}>
                加载中
              </Button>
            </div>
          </div>
        ) : isEmpty(versionList) ? (
          <div style={{ textAlign: 'center', padding: '30px 0' }}>
            <div style={{ marginBottom: 8 }}>暂无版本历史记录</div>
          </div>
        ) : (
          // <div style={{ maxHeight: '600px', overflowY: 'auto' }}>{versionList.map(version => renderVersionTable(version))}</div>
          <div>{versionList.map(version => renderVersionTable(version))}</div>
        )}
      </Modal>

      {/* 导入方案弹窗 */}
      <Modal
        maskClosable={false}
        title="导入方案"
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onOk={handleImportSubmit}
        okText="导入"
        cancelText="取消"
        confirmLoading={importLoading}
      >
        <div style={{ marginBottom: 16 }}>
          <p>请选择要导入的Excel文件（仅支持.xlsx和.xls格式）：</p>
        </div>
        <Upload beforeUpload={beforeUpload} fileList={importFileList} onRemove={() => setImportFileList([])} accept=".xlsx,.xls">
          <Button icon={<UploadOutlined />}>选择文件</Button>
        </Upload>
      </Modal>

      {/* 人选确认弹窗 */}
      <Modal
        maskClosable={false}
        title="人选确认"
        visible={resultConfirmVisible}
        onCancel={() => setResultConfirmVisible(false)}
        onOk={submitResultConfirm}
        okText="确认"
        cancelText="取消"
        confirmLoading={resultConfirmLoading}
        width={1300}
      >
        <div style={{ marginBottom: 16 }}>
          <p>请确认以下结果：</p>
          <p style={{ color: '#ff4d4f' }}>确认后将无法修改，请谨慎操作！</p>
        </div>

        {/* 直接使用 TableComponent，设置为只读模式，使用拷贝数据 */}
        <TableComponent data={resultConfirmData} isReadOnly={true} onDataChange={setResultConfirmData} isResultConfirm={true} />
      </Modal>

      {/* 提交弹窗 */}
      <Modal
        maskClosable={false}
        title="提交方案"
        visible={submitModalVisible}
        onCancel={() => setSubmitModalVisible(false)}
        onOk={handleSubmit}
        okText="提交"
        cancelText="取消"
        confirmLoading={submitLoading}
        width={800}
      >
        <div style={{ marginBottom: 16 }}>
          <p>请选择要提交的接收人：</p>
        </div>
        <ListTable
          data={submitUserList}
          rowKey="id"
          columns={[
            {
              title: '选择',
              key: 'select',
              width: '20%',
              render: (text, record) => (
                <Checkbox
                  checked={selectedUserKeys.includes(record.id)}
                  onChange={e => handleUserSelectChange(e.target.checked ? [...selectedUserKeys, record.id] : selectedUserKeys.filter(key => key !== record.id))}
                />
              ),
            },
            {
              title: '处室',
              dataIndex: 'name',
              key: 'name',
              width: '30%',
            },
            {
              title: '联系方式',
              dataIndex: 'phone',
              key: 'phone',
              width: '30%',
            },
            {
              title: '账户',
              dataIndex: 'account',
              key: 'account',
              width: '30%',
            },
          ]}
        />
      </Modal>
      {/* 共享方案 */}
      <Modal destroyOnClose maskClosable={false} title="共享方案" visible={faVisible} onCancel={() => setFaVisible(false)} footer={null} width={1200}>
        <Gxfa pageType="dqgxfa" planCode={schemeId} doBack={gxfaBack} />
      </Modal>
      {/* 操作日志弹窗 */}
      <Modal destroyOnClose maskClosable={false} title="操作日志" visible={logVisible} onCancel={() => setLogVisible(false)} footer={null} width={1000}>
        <ListTable
          scroll={{ y: 600 }}
          loading={logLoading}
          data={logList}
          rowKey="code"
          columns={[
            {
              title: '序号',
              dataIndex: 'index',
              key: 'index',
              width: '50px',
              align: 'center',
              render: (text, record, index) => {
                return (logPagination['current'] - 1) * logPagination['pageSize'] + index + 1;
              },
            },
            {
              title: '日志内容',
              dataIndex: 'count',
              key: 'count',
              // width: '70%',
            },
            {
              title: '时间',
              dataIndex: 'createTime',
              key: 'createTime',
              width: '200px',
              align: 'center',
            },
          ]}
          pagination={logPagination}
          onChange={handlePageChangeLog}
        />
      </Modal>

      {/* 人员确认信息弹窗 */}
      <ConfirmInfoModal
        visible={confirmModalVisible}
        onCancel={handleConfirmCancel}
        onConfirm={handleConfirmInfoSubmit}
        notShowItemList={currentRowChooseType == '2' ? ['jobChangeCode'] : []}
        personInfo={
          confirmModalData
            ? {
                A0101: confirmModalData?.A0101,
                XRZW: confirmModalData?.currentPosition,
                reason: confirmModalData?.reason,
                introduction: confirmModalData?.introduction,
                jobChangeCode: confirmModalData?.jobChangeCode,
                candidateCode: confirmModalData?.candidateCode,
                jobChange: confirmModalData?.jobChange,
              }
            : undefined
        }
        proposePosition={confirmModalData ? { label: confirmModalData.proposePosition, value: confirmModalData.proposePositionCode } : undefined}
        title="人员信息确认"
        AppointmentData={{
          prepareCadresCode: currentRowData ? currentRowData?.code : undefined,
          planCode: schemeId,
        }}
        isShowTab={true}
      />

      {/* 以岗找人的时候 点击岗位名称弹窗 */}
      <EditPositionModal
        visible={editPositionModalVisible}
        chooseType={currentRowChooseType}
        onCancel={() => setEditPositionModalVisible(false)}
        onConfirm={onConfirmEditPositionModal}
        data={currentRowData}
      ></EditPositionModal>

      {/* 悬浮备选面板 */}
      {alternativePanelVisible && (
        <div
          style={{
            position: 'fixed',
            top: '80px',
            right: '20px',
            width: '300px',
            maxHeight: '500px',
            backgroundColor: '#fff',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: 1000,
            overflow: 'hidden',
          }}
        >
          {/* 标题栏和新增按钮 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '12px 16px',
              borderBottom: '1px solid #f0f0f0',
              backgroundColor: '#fafafa',
            }}
          >
            <span style={{ fontWeight: 'bold', fontSize: '14px' }}>备选人员</span>
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={e => {
                  e.stopPropagation();
                  // 设置为备选人员添加模式
                  setCurrentEditId('alternative-add');
                  setCurrentPersonSelectIndex(0);
                  setCurrentRowChooseType('1');
                  setCurrentRowData(null);
                  // 触发现有的PersonModalSelect
                  if (personSelectButtonRef.current) {
                    personSelectButtonRef.current.click();
                  }
                }}
              >
                新增
              </Button>
            </div>
          </div>

          <div
            style={{
              maxHeight: '400px',
              overflowY: 'auto',
              padding: '8px',
            }}
          >
            {alternativeDtoList.length === 0 ? (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无备选人员" style={{ margin: '20px 0' }} />
            ) : (
              alternativeDtoList.map((alternative, index) => (
                <div
                  key={index}
                  draggable
                  onDragStart={e => {
                    const dragData = {
                      type: 'alternative',
                      subType: 'person',
                      sourceId: 'global',
                      sourceIndex: index,
                      data: alternative,
                    };
                    e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
                  }}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '8px 12px',
                    margin: '4px 0',
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #e9ecef',
                    borderRadius: '4px',
                    cursor: 'move',
                    transition: 'all 0.2s',
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                    <DragOutlined style={{ color: '#999', marginRight: '8px' }} />
                    <div>
                      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>{alternative.name}</div>
                      {alternative.extendDto?.introduction && <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>{alternative.extendDto.introduction}</div>}
                    </div>
                  </div>
                  <DeleteOutlined
                    style={{
                      color: '#ff4d4f',
                      cursor: 'pointer',
                      fontSize: '14px',
                      padding: '4px',
                    }}
                    onClick={e => {
                      e.stopPropagation();
                      handleDeleteAlternative(index);
                    }}
                  />
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* 备注弹窗 */}
      <Modal title="编辑备注" visible={remarkModalVisible} onCancel={handleCloseRemarkModal} onOk={handleSaveRemark} width={600} destroyOnClose>
        <Form form={remarkForm} labelCol={{ span: 4 }}>
          <Form.Item label="任免文件">
            <Upload
              action="/api/base/upload"
              headers={{
                Authorization: sessionStorage.getItem('authorization') || '',
                system: '0',
              }}
              accept=".doc,.docx,.pdf"
              maxCount={1}
              showUploadList={true}
              fileList={rmFileList}
              onChange={info => {
                setRmFileList(info.fileList);
                if (info.file.status === 'done') {
                  const { response: { code = 500, data = [] } = {} } = info.file || {};
                  if (code === 0 && data.length > 0) {
                    message.success('任免文件上传成功');
                  }
                } else if (info.file.status === 'error') {
                  message.error('任免文件上传失败');
                }
              }}
            >
              <Button icon={<UploadOutlined />}>上传任免文件</Button>
            </Upload>
          </Form.Item>

          <Form.Item label="附件文件">
            <Upload
              action="/api/base/upload"
              headers={{
                Authorization: sessionStorage.getItem('authorization') || '',
                system: '0',
              }}
              accept=".doc,.docx,.pdf,.jpg,.jpeg,.png"
              maxCount={1}
              showUploadList={true}
              fileList={fjFileList}
              onChange={info => {
                setFjFileList(info.fileList);
                if (info.file.status === 'done') {
                  const { response: { code = 500, data = [] } = {} } = info.file || {};
                  if (code === 0 && data.length > 0) {
                    message.success('附件文件上传成功');
                  }
                } else if (info.file.status === 'error') {
                  message.error('附件文件上传失败');
                }
              }}
            >
              <Button icon={<UploadOutlined />}>上传附件文件</Button>
            </Upload>
          </Form.Item>

          <Form.Item name="content" label="文字">
            <Input.TextArea placeholder="请输入文字内容" rows={4} maxLength={500} showCount />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

interface NoteProps {
  time: moment.Moment;
  setTime: (time: moment.Moment) => void;
  content: string;
  setContent: (content: string) => void;
}

const Note: React.FC<NoteProps> = ({ time, setTime, content, setContent }) => {
  return (
    <div className={styles.note}>
      <div style={{ display: 'inline-block' }}>
        <TimePicker regNowDate={false} value={time} onChange={setTime} format="YYYY.MM.DD"></TimePicker>
      </div>
      {/* <span
        contentEditable
        style={{
          display: 'inline-block',
          marginLeft: 5,
          outline: 'none',
          borderBottom: '1px dashed transparent',
        }}
        onBlur={e => setContent(e.currentTarget.textContent || '')}
        suppressContentEditableWarning={true}
        onFocus={e => (e.currentTarget.style.borderBottom = '1px dashed #d9d9d9')}
        onMouseLeave={e => (e.currentTarget.style.borderBottom = '1px dashed transparent')}
      >
        {content}
      </span> */}
      {/* 2025/6/9 不让编辑了 只能改时间 */}
      {/* {   !buttons.isHiddenRightTopCard  &&   */}
      <div
        style={{
          display: 'inline-block',
          marginLeft: 5,
          outline: 'none',
          borderBottom: '1px dashed transparent',
        }}
      >{`向${content}汇报本批市管干部调整动议初步考虑。`}</div>
      {/* } */}
    </div>
  );
};
interface FileAsideProps {}
const FileAside: React.FC<FileAsideProps> = ({}) => {
  // 存储选中项的状态
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  // 控制悬浮框显示/隐藏
  const [showFloatPanel, setShowFloatPanel] = useState<boolean>(false);
  // 模拟文件数据
  const [fileList, setFileList] = useState<Array<{ id: number; name: string }>>([
    { id: 1, name: 'XXXXXX.rar' },
    { id: 2, name: 'XXXXXX.rar' },
    { id: 3, name: 'XXXXXX.rar' },
    { id: 4, name: 'XXXXXX.rar' },
    { id: 5, name: 'XXXXXX.rar' },
    { id: 6, name: 'XXXXXX.rar' },
  ]);

  // 材料目录数据，仅包含内容项
  const fileData = [
    { id: 2, name: '九龙坡区法院' },
    { id: 3, name: '九龙坡区法院' },
    { id: 4, name: '九龙坡区法院' },
  ];

  // 处理标题点击事件
  const handleHeaderClick = () => {
    console.log('点击了标题');
  };

  // 处理内容点击事件
  const handleContentClick = (index: number) => {
    setSelectedIndex(index);
    console.log('点击了内容项:', fileData[index].name);
    setShowFloatPanel(true);
  };

  // 关闭悬浮框
  const closeFloatPanel = () => {
    setShowFloatPanel(false);
  };

  // 处理点击外部关闭悬浮框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const floatPanel = document.getElementById('floatPanel');
      const fileItems = document.querySelectorAll('.contentItem');

      if (floatPanel && !floatPanel.contains(event.target as Node) && !Array.from(fileItems).some(item => item.contains(event.target as Node))) {
        closeFloatPanel();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 上传文件处理
  const handleUploadFile = () => {
    console.log('上传文件');
    // 这里应该打开文件选择对话框并处理上传逻辑
  };

  // 下载文件处理
  const handleDownloadFile = (fileId: number) => {
    console.log('下载文件:', fileId);
  };

  // 删除文件处理
  const handleDeleteFile = (fileId: number) => {
    console.log('删除文件:', fileId);
    setFileList(fileList.filter(file => file.id !== fileId));
  };

  return (
    <div className={styles.fileAside}>
      <div className={styles.fileList}>
        {/* 标题项 - 直接写在HTML中 */}
        <div className={`${styles.fileItem} ${styles.headerItem}`} onClick={handleHeaderClick}>
          材料目录
        </div>
        <Divider style={{ margin: '10px 0' }}></Divider>
        {/* 内容项 - 通过数据渲染 */}
        {fileData.map((item, index) => (
          <div key={item.id} className={`${styles.fileItem} ${styles.contentItem} ${selectedIndex === index ? styles.selectedItem : ''}`} onClick={e => handleContentClick(index)}>
            <div className={styles.fileItemContent}>
              <img src={require('@/assets/beInOffice/dyFileimg.png')} alt="文件图标" className={styles.fileIcon} />
              <span className={styles.fileName}>{item.name}</span>
            </div>
          </div>
        ))}
        {/* 悬浮框 */}
        {showFloatPanel && (
          <div id="floatPanel" className={styles.floatPanel}>
            <div className={styles.floatPanelHeader}>
              <span>{fileData[selectedIndex]?.name || ''}</span>
              <button className={styles.closeButton} onClick={closeFloatPanel}>
                ×
              </button>
            </div>
            <div className={styles.uploadButtonContainer}>
              <Button icon={<UploadOutlined />} type="primary" onClick={handleUploadFile}>
                上传文件
              </Button>
            </div>
            <div className={styles.fileListContainer}>
              {fileList.map(file => (
                <div key={file.id} className={styles.floatFileItem}>
                  <span className={styles.floatFileName}>{file.name}</span>
                  <div className={styles.fileActions}>
                    <button className={styles.downloadButton} onClick={() => handleDownloadFile(file.id)}>
                      <img src={require('@/assets/beInOffice/fileDownloadImg.png')} alt="下载" className={styles.actionIcon} />
                    </button>
                    <button className={styles.deleteButton} onClick={() => handleDeleteFile(file.id)}>
                      <img src={require('@/assets/beInOffice/fileDelectImg.png')} alt="删除" className={styles.actionIcon} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConsiderationComponent;
